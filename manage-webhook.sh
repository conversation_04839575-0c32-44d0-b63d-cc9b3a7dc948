#!/bin/bash

# Telegram Bot Webhook Management Script

BOT_TOKEN="7450291334:AAEeZJzQS0vTx2f2dTCHatFxPx_Dg56zows"
WEBHOOK_URL="https://cpcheckbot.thaipay.info/telegram-webhook"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

show_usage() {
    echo -e "${BLUE}🤖 Telegram Bot Webhook Manager${NC}"
    echo "Usage: $0 {set|get|delete|test}"
    echo ""
    echo "Commands:"
    echo "  set     - Set webhook URL"
    echo "  get     - Get current webhook info"
    echo "  delete  - Delete webhook (switch to polling mode)"
    echo "  test    - Test webhook connectivity"
}

set_webhook() {
    echo -e "${BLUE}📡 Setting webhook...${NC}"
    response=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/setWebhook" \
         -H "Content-Type: application/json" \
         -d "{\"url\": \"$WEBHOOK_URL\"}")
    
    if echo "$response" | grep -q '"ok":true'; then
        echo -e "${GREEN}✅ Webhook set successfully!${NC}"
        echo "URL: $WEBHOOK_URL"
    else
        echo -e "${RED}❌ Failed to set webhook${NC}"
        echo "Response: $response"
    fi
}

get_webhook() {
    echo -e "${BLUE}🔍 Getting webhook info...${NC}"
    response=$(curl -s "https://api.telegram.org/bot$BOT_TOKEN/getWebhookInfo")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

delete_webhook() {
    echo -e "${BLUE}🗑️  Deleting webhook...${NC}"
    response=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/deleteWebhook")
    
    if echo "$response" | grep -q '"ok":true'; then
        echo -e "${GREEN}✅ Webhook deleted successfully!${NC}"
        echo -e "${YELLOW}⚠️  Bot is now in polling mode${NC}"
    else
        echo -e "${RED}❌ Failed to delete webhook${NC}"
        echo "Response: $response"
    fi
}

test_webhook() {
    echo -e "${BLUE}🧪 Testing webhook connectivity...${NC}"
    
    # Test if URL is accessible
    echo "Testing URL accessibility..."
    if curl -s -o /dev/null -w "%{http_code}" "$WEBHOOK_URL" | grep -q "200\|404\|405"; then
        echo -e "${GREEN}✅ Webhook URL is accessible${NC}"
    else
        echo -e "${RED}❌ Webhook URL is not accessible${NC}"
    fi
    
    # Get webhook info
    echo ""
    echo "Current webhook status:"
    get_webhook
}

case "$1" in
    set)
        set_webhook
        ;;
    get)
        get_webhook
        ;;
    delete)
        delete_webhook
        ;;
    test)
        test_webhook
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

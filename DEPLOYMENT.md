# 部署指南

本文档提供了Telegram订单处理机器人的完整部署指南。

## 部署前检查清单

### 1. 环境准备

- [ ] 服务器已安装Docker和Docker Compose
- [ ] 服务器已安装Go 1.21+（如果需要本地构建）
- [ ] 服务器网络可以访问Telegram API
- [ ] 服务器网络可以访问第三方API
- [ ] SSL证书已配置（用于Webhook）

### 2. 配置文件准备

- [ ] 复制`.env.example`为`.env`
- [ ] 配置Telegram Bot Token
- [ ] 配置第三方API相关参数
- [ ] 配置二维码API相关参数
- [ ] 配置所有群组ID
- [ ] 运行`make check-config`验证配置

### 3. 必需的环境变量

#### 基础配置
```bash
PORT=8080                                    # 服务端口
TELEGRAM_BOT_TOKEN=your_bot_token_here       # Telegram Bot Token
```

#### 第三方API配置
```bash
THIRD_PARTY_API_BASE_URL=https://aut.mb.rushingpay.com
THIRD_PARTY_API_QUERY_PATH=/ordercheck/query
THIRD_API_KEY=your_api_key_here
```

#### 二维码API配置
```bash
QRCODE_API_ACCOUNT_NO=your_account_no_here
QRCODE_API_BASE_URL=https://api-fin.uexchange.io/v1/fin/bank/
QRCODE_API_LICENSE_KEY=your_license_key_here
QRCODE_API_ACCESS_KEY=your_access_key_here
```

#### 群组配置
```bash
CUSTOMER_SERVICE_GROUP=-*************        # 人工客服群ID
SUPPLIER_GROUP_TOPPAY=-*************         # TopPay供应商群ID
SUPPLIER_GROUP_VADERPAY=-*************       # VaderPay供应商群ID
SUPPLIER_GROUP_THPAY=-*************          # THPay供应商群ID
SUPPLIER_GROUP_EXKUB=-*************          # Exkub供应商群ID
```

## 部署方式

### 方式1: Docker Compose部署（推荐）

1. **配置检查**
```bash
make check-config
```

2. **构建和启动**
```bash
make compose-up
```

3. **查看日志**
```bash
make compose-logs
```

4. **设置Webhook**
```bash
export BOT_TOKEN=your_bot_token_here
export WEBHOOK_URL=https://yourdomain.com
make setup-webhook
```

### 方式2: 本地部署

1. **安装依赖**
```bash
make deps
```

2. **构建应用**
```bash
make build
```

3. **运行应用**
```bash
make run
```

### 方式3: 使用设置脚本

1. **运行设置脚本**
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

2. **选择"完整设置"选项**

## 部署后验证

### 1. 健康检查
```bash
make health
# 或直接访问
curl http://localhost:8080/health
```

### 2. 检查日志
```bash
# Docker环境
make compose-logs

# 本地环境
tail -f logs/app.log
```

### 3. 验证Webhook
```bash
export BOT_TOKEN=your_bot_token_here
make get-webhook
```

### 4. 测试机器人功能
- 在配置的群组中发送测试消息
- 检查机器人是否正确响应
- 验证订单处理流程

## 常见问题排查

### 1. Webhook设置失败
- 检查域名是否可访问
- 确认SSL证书有效
- 验证Bot Token正确性

### 2. 机器人无响应
- 检查群组ID配置
- 确认机器人在群组中有必要权限
- 查看应用日志排查错误

### 3. API调用失败
- 验证第三方API配置
- 检查网络连接
- 确认API密钥有效性

### 4. 二维码识别失败
- 确保图片清晰
- 检查二维码API配置
- 验证API访问权限

## 监控和维护

### 1. 日志监控
```bash
# 实时查看日志
make compose-logs

# 检查错误日志
grep -i error logs/app.log
```

### 2. 性能监控
- 监控CPU和内存使用
- 检查API响应时间
- 监控处理的订单数量

### 3. 定期维护
- 定期清理旧日志文件
- 更新依赖包
- 备份配置文件

## 安全注意事项

1. **保护敏感信息**
   - 不要将`.env`文件提交到版本控制
   - 定期更换API密钥
   - 使用强密码和安全的Bot Token

2. **网络安全**
   - 使用HTTPS进行Webhook通信
   - 限制服务器访问权限
   - 定期更新系统和依赖

3. **权限管理**
   - 确保机器人只有必要的群组权限
   - 定期审查群组成员
   - 监控异常活动

## 更新和升级

### 1. 代码更新
```bash
git pull origin main
make compose-down
make compose-up
```

### 2. 配置更新
```bash
# 检查新的配置项
make check-config

# 重启服务
make compose-down
make compose-up
```

### 3. 依赖更新
```bash
go mod tidy
make docker-build
make compose-up
```

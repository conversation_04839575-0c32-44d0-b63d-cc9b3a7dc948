package services

import (
	"strconv"
	"testing"
	"telegram-order-bot/internal/models"
)

// TestGroupCacheCleanup 测试群组缓存清除功能
func TestGroupCacheCleanup(t *testing.T) {
	// 创建一个模拟的BotService实例
	botService := &BotService{}

	// 模拟一些缓存数据
	testChatID := int64(-1001234567890)
	testTelegramGroupID := strconv.FormatInt(testChatID, 10)

	// 添加测试数据到缓存
	botService.groupTypes.Store(testChatID, models.GroupTypeMerchant)
	botService.groupNames.Store(testChatID, "Test Merchant Group")

	// 验证缓存中有数据
	if _, ok := botService.groupTypes.Load(testChatID); !ok {
		t.Error("Group type should be cached")
	}
	if _, ok := botService.groupNames.Load(testChatID); !ok {
		t.Error("Group name should be cached")
	}

	// 调用缓存清除方法
	botService.clearGroupCache(testTelegramGroupID)

	// 验证缓存已被清除
	if _, ok := botService.groupTypes.Load(testChatID); ok {
		t.Error("Group type cache should be cleared")
	}
	if _, ok := botService.groupNames.Load(testChatID); ok {
		t.Error("Group name cache should be cleared")
	}
}

// TestGroupCacheCleanupInvalidID 测试无效ID的缓存清除
func TestGroupCacheCleanupInvalidID(t *testing.T) {
	botService := &BotService{}

	// 测试无效的telegram group ID
	invalidID := "invalid_id"

	// 这应该不会导致panic，而是记录错误日志
	botService.clearGroupCache(invalidID)

	// 如果没有panic，测试通过
}

// TestGroupServiceCacheCallback 测试GroupService的缓存回调设置
func TestGroupServiceCacheCallback(t *testing.T) {
	// 创建GroupService实例（不需要真实的数据库连接）
	groupService := &GroupService{}

	// 测试回调函数
	var callbackCalled bool
	var callbackGroupID string

	callback := func(telegramGroupID string) {
		callbackCalled = true
		callbackGroupID = telegramGroupID
	}

	// 设置回调函数
	groupService.SetCacheCleanupCallback(callback)

	// 验证回调函数已设置
	if groupService.onGroupDeleted == nil {
		t.Error("Cache cleanup callback should be set")
	}

	// 模拟调用回调函数
	testGroupID := "-1001234567890"
	if groupService.onGroupDeleted != nil {
		groupService.onGroupDeleted(testGroupID)
	}

	// 验证回调函数被调用
	if !callbackCalled {
		t.Error("Callback function should be called")
	}
	if callbackGroupID != testGroupID {
		t.Errorf("Expected group ID %s, got %s", testGroupID, callbackGroupID)
	}
}

// TestBotServiceGroupServiceIntegration 测试BotService和GroupService的集成
func TestBotServiceGroupServiceIntegration(t *testing.T) {
	// 创建GroupService实例
	groupService := &GroupService{}

	// 创建BotService实例并设置GroupService
	botService := &BotService{
		groupService: groupService,
	}

	// 模拟NewBotService中的回调设置逻辑
	if groupService != nil {
		groupService.SetCacheCleanupCallback(botService.clearGroupCache)
	}

	// 验证回调函数已正确设置
	if groupService.onGroupDeleted == nil {
		t.Error("Cache cleanup callback should be set in group service")
	}

	// 添加测试缓存数据
	testChatID := int64(-1001234567890)
	testTelegramGroupID := strconv.FormatInt(testChatID, 10)
	botService.groupTypes.Store(testChatID, models.GroupTypeMerchant)
	botService.groupNames.Store(testChatID, "Test Group")

	// 通过GroupService触发缓存清除
	if groupService.onGroupDeleted != nil {
		groupService.onGroupDeleted(testTelegramGroupID)
	}

	// 验证缓存已被清除
	if _, ok := botService.groupTypes.Load(testChatID); ok {
		t.Error("Group type cache should be cleared through group service callback")
	}
	if _, ok := botService.groupNames.Load(testChatID); ok {
		t.Error("Group name cache should be cleared through group service callback")
	}
}

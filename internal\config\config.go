package config

import (
	"os"
	"strconv"
	"strings"
)

// Config 应用配置结构
type Config struct {
	Port                 string
	TelegramBotToken     string
	ThirdPartyAPIConfig  ThirdPartyAPIConfig
	QRCodeAPIConfig      QRCodeAPIConfig
	LogConfig            LogConfig
	DatabaseConfig       DatabaseConfig
	EnableStatusMessages bool // 是否启用详细状态消息（false时使用简洁消息）
}

// ThirdPartyAPIConfig 第三方API配置
type ThirdPartyAPIConfig struct {
	BaseURL   string
	QueryPath string
	Key       string
}

type QRCodeAPIConfig struct {
	BaseURL    string
	AccountNo  string
	LicenseKey string
	AccessKey  string
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string // 日志级别: debug, info, warn, error
	FilePath   string // 日志文件路径
	MaxSize    int    // 单个日志文件最大大小(MB)
	MaxBackups int    // 保留的旧日志文件数量
	MaxAge     int    // 保留日志文件的最大天数
	Compress   bool   // 是否压缩旧日志文件
	Console    bool   // 是否同时输出到控制台
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string // 数据库主机
	Port     string // 数据库端口
	User     string // 数据库用户名
	Password string // 数据库密码
	Database string // 数据库名称
	Charset  string // 字符集，默认utf8mb4
}

// Load 加载配置
func Load() *Config {
	return &Config{
		Port:             getEnv("PORT", "8080"),
		TelegramBotToken: getEnv("TELEGRAM_BOT_TOKEN", ""),
		ThirdPartyAPIConfig: ThirdPartyAPIConfig{
			BaseURL:   getEnv("THIRD_PARTY_API_BASE_URL", ""),
			QueryPath: getEnv("THIRD_PARTY_API_QUERY_PATH", ""),
			Key:       getEnv("THIRD_API_KEY", ""),
		},
		QRCodeAPIConfig: QRCodeAPIConfig{
			BaseURL:    getEnv("QRCODE_API_BASE_URL", ""),
			AccountNo:  getEnv("QRCODE_API_ACCOUNT_NO", ""),
			LicenseKey: getEnv("QRCODE_API_LICENSE_KEY", ""),
			AccessKey:  getEnv("QRCODE_API_ACCESS_KEY", ""),
		},

		LogConfig: LogConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			FilePath:   getEnv("LOG_FILE_PATH", "./logs/app.log"),
			MaxSize:    getEnvInt("LOG_MAX_SIZE", 100),   // 100MB
			MaxBackups: getEnvInt("LOG_MAX_BACKUPS", 3),  // 保留3个备份
			MaxAge:     getEnvInt("LOG_MAX_AGE", 28),     // 保留28天
			Compress:   getEnvBool("LOG_COMPRESS", true), // 压缩旧日志
			Console:    getEnvBool("LOG_CONSOLE", true),  // 同时输出到控制台
		},
		DatabaseConfig: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "3306"),
			User:     getEnv("DB_USER", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			Database: getEnv("DB_NAME", "telegram_order_bot"),
			Charset:  getEnv("DB_CHARSET", "utf8mb4"),
		},
		EnableStatusMessages: getEnvBool("ENABLE_STATUS_MESSAGES", false),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数类型的环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔类型的环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		lowerValue := strings.ToLower(value)
		if lowerValue == "true" || lowerValue == "1" || lowerValue == "yes" {
			return true
		}
		if lowerValue == "false" || lowerValue == "0" || lowerValue == "no" {
			return false
		}
	}
	return defaultValue
}

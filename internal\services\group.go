package services

import (
	"fmt"
	"strconv"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"

	"gorm.io/gorm"
)

// PaginationRequest 分页请求参数
type PaginationRequest struct {
	Page               int               `json:"page" form:"page"`                               // 页码，从1开始
	PageSize           int               `json:"page_size" form:"page_size"`                     // 每页大小
	GroupType          *models.GroupType `json:"group_type" form:"group_type"`                   // 群组类型过滤
	PaymentInstitution string            `json:"payment_institution" form:"payment_institution"` // 支付机构过滤
	IsActive           *bool             `json:"is_active" form:"is_active"`                     // 是否活跃过滤
	Search             string            `json:"search" form:"search"`                           // 搜索关键词（群组名称）
}

// PaginationResponse 分页响应结果
type PaginationResponse struct {
	Data       []models.Group `json:"data"`        // 数据列表
	Total      int64          `json:"total"`       // 总记录数
	Page       int            `json:"page"`        // 当前页码
	PageSize   int            `json:"page_size"`   // 每页大小
	TotalPages int            `json:"total_pages"` // 总页数
}

// GroupService 群聊服务
type GroupService struct {
	db             *gorm.DB
	onGroupDeleted func(telegramGroupID string) // 群组删除时的缓存清除回调函数
}

// NewGroupService 创建群聊服务
func NewGroupService(db *gorm.DB) *GroupService {
	return &GroupService{
		db: db,
	}
}

// SetCacheCleanupCallback 设置缓存清除回调函数
func (gs *GroupService) SetCacheCleanupCallback(callback func(telegramGroupID string)) {
	gs.onGroupDeleted = callback
}

// GetGroupByID 根据ID获取群聊信息
func (gs *GroupService) GetGroupByID(id uint) (*models.Group, error) {
	var group models.Group
	err := gs.db.Where("id = ?", id).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.WithField("group_id", id).Warn("Group not found in database")
			return nil, nil
		}
		logger.WithFields(map[string]interface{}{
			"group_id": id,
			"error":    err,
		}).Error("Failed to get group by ID")
		return nil, err
	}
	return &group, nil
}

// GetGroupByTelegramID 根据Telegram群组ID获取群聊信息
func (gs *GroupService) GetGroupByTelegramID(telegramGroupID string) (*models.Group, error) {
	var group models.Group
	err := gs.db.Where("telegram_group_id = ?", telegramGroupID).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.WithField("telegram_group_id", telegramGroupID).Warn("Group not found in database")
			return nil, nil
		}
		logger.WithFields(map[string]interface{}{
			"telegram_group_id": telegramGroupID,
			"error":             err,
		}).Error("Failed to get group by telegram ID")
		return nil, err
	}
	return &group, nil
}

// GetMerchantGroups 获取所有商户群
func (gs *GroupService) GetMerchantGroups() ([]models.Group, error) {
	var groups []models.Group
	err := gs.db.Where("group_type = ? AND is_active = ?", models.GroupTypeMerchant, true).Find(&groups).Error
	if err != nil {
		logger.WithField("error", err).Error("Failed to get merchant groups")
		return nil, err
	}
	return groups, nil
}

// GetSupplierGroups 获取所有供应商群
func (gs *GroupService) GetSupplierGroups() ([]models.Group, error) {
	var groups []models.Group
	err := gs.db.Where("group_type = ? AND is_active = ?", models.GroupTypeSupplier, true).Find(&groups).Error
	if err != nil {
		logger.WithField("error", err).Error("Failed to get supplier groups")
		return nil, err
	}
	return groups, nil
}

// GetCustomerServiceGroups 获取所有客服群
func (gs *GroupService) GetCustomerServiceGroups() ([]models.Group, error) {
	var groups []models.Group
	err := gs.db.Where("group_type = ? AND is_active = ?", models.GroupTypeCustomerService, true).Find(&groups).Error
	if err != nil {
		logger.WithField("error", err).Error("Failed to get customer service groups")
		return nil, err
	}
	return groups, nil
}

// GetSupplierGroupsByPaymentInstitution 根据支付机构获取供应商群
func (gs *GroupService) GetSupplierGroupsByPaymentInstitution(paymentInstitution string) ([]models.Group, error) {
	var groups []models.Group
	err := gs.db.Where("group_type = ? AND payment_institution = ? AND is_active = ?",
		models.GroupTypeSupplier, paymentInstitution, true).Find(&groups).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"payment_institution": paymentInstitution,
			"error":               err,
		}).Error("Failed to get supplier groups by payment institution")
		return nil, err
	}
	return groups, nil
}

// IsMerchantGroup 判断群组是否为商户群
func (gs *GroupService) IsMerchantGroup(telegramGroupID string) (bool, error) {
	group, err := gs.GetGroupByTelegramID(telegramGroupID)
	if err != nil {
		return false, err
	}
	if group == nil {
		return false, nil
	}
	return group.IsMerchantGroup(), nil
}

// IsSupplierGroup 判断群组是否为供应商群
func (gs *GroupService) IsSupplierGroup(telegramGroupID string) (bool, error) {
	group, err := gs.GetGroupByTelegramID(telegramGroupID)
	if err != nil {
		return false, err
	}
	if group == nil {
		return false, nil
	}
	return group.IsSupplierGroup(), nil
}

// IsCustomerServiceGroup 判断群组是否为客服群
func (gs *GroupService) IsCustomerServiceGroup(telegramGroupID string) (bool, error) {
	group, err := gs.GetGroupByTelegramID(telegramGroupID)
	if err != nil {
		return false, err
	}
	if group == nil {
		return false, nil
	}
	return group.IsCustomerServiceGroup(), nil
}

// GetGroupName 获取群组名称
func (gs *GroupService) GetGroupName(telegramGroupID string) (string, error) {
	group, err := gs.GetGroupByTelegramID(telegramGroupID)
	if err != nil {
		return "", err
	}
	if group == nil {
		// 如果数据库中没有找到，返回默认名称
		return fmt.Sprintf("未知群组(%s)", telegramGroupID), nil
	}
	return group.GroupName, nil
}

// GetGroupNameByID 根据数字ID获取群组名称（兼容旧代码）
func (gs *GroupService) GetGroupNameByID(chatID int64) (string, error) {
	telegramGroupID := strconv.FormatInt(chatID, 10)
	return gs.GetGroupName(telegramGroupID)
}

// CreateGroup 创建新群组
func (gs *GroupService) CreateGroup(group *models.Group) error {
	err := gs.db.Create(group).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"group_name":        group.GroupName,
			"telegram_group_id": group.TelegramGroupID,
			"group_type":        group.GroupType,
			"error":             err,
		}).Error("Failed to create group")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"group_id":          group.ID,
		"group_name":        group.GroupName,
		"telegram_group_id": group.TelegramGroupID,
		"group_type":        group.GroupType.String(),
	}).Info("Group created successfully")

	return nil
}

// UpdateGroup 更新群组信息
func (gs *GroupService) UpdateGroup(group *models.Group) error {
	err := gs.db.Save(group).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"group_id": group.ID,
			"error":    err,
		}).Error("Failed to update group")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"group_id":          group.ID,
		"group_name":        group.GroupName,
		"telegram_group_id": group.TelegramGroupID,
	}).Info("Group updated successfully")

	// 通知缓存清除（如果有缓存清除回调函数）
	// 更新群组信息时需要清除缓存以确保获取最新数据
	if gs.onGroupDeleted != nil {
		gs.onGroupDeleted(group.TelegramGroupID)
	}

	return nil
}

// DeleteGroup 软删除群组（设置为不活跃）
func (gs *GroupService) DeleteGroup(id uint) error {
	// 首先获取群组信息，用于清除缓存
	var group models.Group
	err := gs.db.Where("id = ?", id).First(&group).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"group_id": id,
			"error":    err,
		}).Error("Failed to get group before deletion")
		return err
	}

	// 执行软删除
	err = gs.db.Model(&models.Group{}).Where("id = ?", id).Update("is_active", false).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"group_id": id,
			"error":    err,
		}).Error("Failed to delete group")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"group_id":          id,
		"group_name":        group.GroupName,
		"telegram_group_id": group.TelegramGroupID,
	}).Info("Group deleted successfully")

	// 通知缓存清除（如果有缓存清除回调函数）
	if gs.onGroupDeleted != nil {
		gs.onGroupDeleted(group.TelegramGroupID)
	}

	return nil
}

// HardDeleteGroup 硬删除群组（从数据库中彻底删除）
func (gs *GroupService) HardDeleteGroup(id uint) error {
	// 首先获取群组信息，用于清除缓存
	var group models.Group
	err := gs.db.Where("id = ?", id).First(&group).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"group_id": id,
			"error":    err,
		}).Error("Failed to get group before hard deletion")
		return err
	}

	// 执行硬删除
	err = gs.db.Delete(&models.Group{}, id).Error
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"group_id": id,
			"error":    err,
		}).Error("Failed to hard delete group")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"group_id":          id,
		"group_name":        group.GroupName,
		"telegram_group_id": group.TelegramGroupID,
	}).Info("Group hard deleted successfully")

	// 通知缓存清除（如果有缓存清除回调函数）
	if gs.onGroupDeleted != nil {
		gs.onGroupDeleted(group.TelegramGroupID)
	}

	return nil
}

// GetGroupsWithPagination 分页查询群组
func (gs *GroupService) GetGroupsWithPagination(req *PaginationRequest) (*PaginationResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大页面大小
	}

	// 构建查询条件
	query := gs.db.Model(&models.Group{})

	// 群组类型过滤
	if req.GroupType != nil {
		query = query.Where("group_type = ?", *req.GroupType)
	}

	// 支付机构过滤
	if req.PaymentInstitution != "" {
		query = query.Where("payment_institution = ?", req.PaymentInstitution)
	}

	// 活跃状态过滤
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	} else {
		// 默认只查询活跃的群组
		query = query.Where("is_active = ?", true)
	}

	// 搜索关键词过滤（群组名称）
	if req.Search != "" {
		query = query.Where("group_name LIKE ?", "%"+req.Search+"%")
	}

	// 获取总记录数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.WithField("error", err).Error("Failed to count groups")
		return nil, err
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	// 分页查询
	var groups []models.Group
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&groups).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get groups with pagination")
		return nil, err
	}

	return &PaginationResponse{
		Data:       groups,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetMerchantGroupsWithPagination 分页查询商户群
func (gs *GroupService) GetMerchantGroupsWithPagination(page, pageSize int, search string) (*PaginationResponse, error) {
	groupType := models.GroupTypeMerchant
	req := &PaginationRequest{
		Page:      page,
		PageSize:  pageSize,
		GroupType: &groupType,
		Search:    search,
	}
	return gs.GetGroupsWithPagination(req)
}

// GetSupplierGroupsWithPagination 分页查询供应商群
func (gs *GroupService) GetSupplierGroupsWithPagination(page, pageSize int, paymentInstitution, search string) (*PaginationResponse, error) {
	groupType := models.GroupTypeSupplier
	req := &PaginationRequest{
		Page:               page,
		PageSize:           pageSize,
		GroupType:          &groupType,
		PaymentInstitution: paymentInstitution,
		Search:             search,
	}
	return gs.GetGroupsWithPagination(req)
}

// GetCustomerServiceGroupsWithPagination 分页查询客服群
func (gs *GroupService) GetCustomerServiceGroupsWithPagination(page, pageSize int, search string) (*PaginationResponse, error) {
	groupType := models.GroupTypeCustomerService
	req := &PaginationRequest{
		Page:      page,
		PageSize:  pageSize,
		GroupType: &groupType,
		Search:    search,
	}
	return gs.GetGroupsWithPagination(req)
}

<template>
  <AlertDialog v-model:open="dialogOpen">
    <template #title>
      确认删除群组
    </template>
    
    <template #description>
      <div class="space-y-3">
        <p>您确定要删除以下群组吗？此操作将会禁用该群组，但不会永久删除数据。</p>
        
        <div v-if="group" class="bg-muted p-3 rounded-md space-y-2">
          <div class="flex justify-between">
            <span class="font-medium">群组名称：</span>
            <span>{{ group.group_name }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium">Telegram ID：</span>
            <span class="font-mono text-sm">{{ group.telegram_group_id }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium">群组类型：</span>
            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
              :class="getGroupTypeClass(group.group_type)">
              {{ getGroupTypeLabel(group.group_type) }}
            </span>
          </div>
          <div v-if="group.payment_institution" class="flex justify-between">
            <span class="font-medium">支付机构：</span>
            <span>{{ group.payment_institution }}</span>
          </div>
        </div>
        
        <p class="text-sm text-muted-foreground">
          <strong>注意：</strong>删除后该群组将被标记为禁用状态，不再接收和处理消息。如需恢复，可以通过编辑功能重新启用。
        </p>
      </div>
    </template>
    
    <template #actions>
      <Button variant="outline" @click="handleCancel" :disabled="deleting">
        取消
      </Button>
      <Button variant="destructive" @click="handleConfirm" :disabled="deleting">
        <div v-if="deleting" class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          删除中...
        </div>
        <span v-else>确认删除</span>
      </Button>
    </template>
  </AlertDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import AlertDialog from '@/components/ui/alert-dialog.vue'
import Button from '@/components/ui/button.vue'
import { GroupService } from '@/services/groupService'
import type { Group, GroupType } from '@/types/group'
import { GroupTypeLabels } from '@/types/group'

interface Props {
  open: boolean
  group?: Group | null
}

interface Emits {
  'update:open': [value: boolean]
  success: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框开关状态
const dialogOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

// 删除状态
const deleting = ref(false)

// 获取群组类型标签
const getGroupTypeLabel = (type: GroupType): string => {
  return GroupTypeLabels[type] || '未知'
}

// 获取群组类型样式
const getGroupTypeClass = (type: GroupType): string => {
  const classes = {
    [1]: 'bg-blue-100 text-blue-800',
    [2]: 'bg-green-100 text-green-800',
    [3]: 'bg-purple-100 text-purple-800'
  }
  return classes[type] || 'bg-gray-100 text-gray-800'
}

// 处理确认删除
const handleConfirm = async () => {
  if (!props.group) return

  try {
    deleting.value = true
    await GroupService.deleteGroup(props.group.id)
    emit('success')
  } catch (error) {
    console.error('删除群组失败:', error)
    // 这里可以添加错误提示
  } finally {
    deleting.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('update:open', false)
}
</script>

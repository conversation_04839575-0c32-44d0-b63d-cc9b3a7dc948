<template>
  <div class="space-y-6">
    <!-- 页面标题和操作按钮 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">群组管理</h1>
        <p class="text-muted-foreground">管理Telegram群组配置</p>
      </div>
      <Button @click="openCreateDialog" class="flex items-center gap-2">
        <Plus class="h-4 w-4" />
        新增群组
      </Button>
    </div>

    <!-- 搜索和筛选 -->
    <Card class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <Label for="search">搜索群组名称</Label>
          <Input
            id="search"
            v-model="searchQuery"
            placeholder="输入群组名称..."
            @input="debouncedSearch"
          />
        </div>
        <div>
          <Label for="groupType">群组类型</Label>
          <select
            id="groupType"
            v-model="filters.group_type"
            @change="loadGroups"
            class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <option value="">全部类型</option>
            <option value="1">商户群</option>
            <option value="2">供应商群</option>
            <option value="3">客服群</option>
          </select>
        </div>
        <div>
          <Label for="paymentInstitution">支付机构</Label>
          <Input
            id="paymentInstitution"
            v-model="filters.payment_institution"
            placeholder="支付机构..."
            @input="debouncedSearch"
          />
        </div>
        <div>
          <Label for="isActive">状态</Label>
          <select
            id="isActive"
            v-model="filters.is_active"
            @change="loadGroups"
            class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <option value="">全部状态</option>
            <option value="true">启用</option>
            <option value="false">禁用</option>
          </select>
        </div>
      </div>
    </Card>

    <!-- 群组列表 -->
    <Card>
      <div class="p-6">
        <div class="rounded-md border">
          <table class="w-full">
            <thead>
              <tr class="border-b bg-muted/50">
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">ID</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">群组名称</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Telegram ID</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">类型</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">支付机构</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">状态</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">创建时间</th>
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="loading" class="border-b">
                <td colspan="8" class="h-24 px-4 text-center">
                  <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span class="ml-2">加载中...</span>
                  </div>
                </td>
              </tr>
              <tr v-else-if="groups.length === 0" class="border-b">
                <td colspan="8" class="h-24 px-4 text-center text-muted-foreground">
                  暂无数据
                </td>
              </tr>
              <tr v-else v-for="group in groups" :key="group.id" class="border-b hover:bg-muted/50">
                <td class="p-4 align-middle">{{ group.id }}</td>
                <td class="p-4 align-middle font-medium">{{ group.group_name }}</td>
                <td class="p-4 align-middle font-mono text-sm">{{ group.telegram_group_id }}</td>
                <td class="p-4 align-middle">
                  <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
                    :class="getGroupTypeClass(group.group_type)">
                    {{ getGroupTypeLabel(group.group_type) }}
                  </span>
                </td>
                <td class="p-4 align-middle">{{ group.payment_institution || '-' }}</td>
                <td class="p-4 align-middle">
                  <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
                    :class="group.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                    {{ group.is_active ? '启用' : '禁用' }}
                  </span>
                </td>
                <td class="p-4 align-middle text-sm text-muted-foreground">
                  {{ formatDate(group.created_at) }}
                </td>
                <td class="p-4 align-middle">
                  <div class="flex items-center gap-2">
                    <Button variant="ghost" size="sm" @click="openEditDialog(group)">
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" @click="openDeleteDialog(group)">
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="flex items-center justify-between space-x-2 py-4">
          <div class="text-sm text-muted-foreground">
            共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.total_pages }} 页
          </div>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.page <= 1"
              @click="changePage(pagination.page - 1)"
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.page >= pagination.total_pages"
              @click="changePage(pagination.page + 1)"
            >
              下一页
            </Button>
          </div>
        </div>
      </div>
    </Card>

    <!-- 新增/编辑对话框 -->
    <GroupFormDialog
      v-model:open="formDialogOpen"
      :group="selectedGroup"
      @success="handleFormSuccess"
    />

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      v-model:open="deleteDialogOpen"
      :group="selectedGroup"
      @success="handleDeleteSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { debounce } from '@vueuse/core'
import { Plus, Edit, Trash2 } from 'lucide-vue-next'
import Button from '@/components/ui/button.vue'
import Input from '@/components/ui/input.vue'
import Label from '@/components/ui/label.vue'
import Card from '@/components/ui/card.vue'
import GroupFormDialog from '@/components/GroupFormDialog.vue'
import DeleteConfirmDialog from '@/components/DeleteConfirmDialog.vue'
import { GroupService } from '@/services/groupService'
import type { Group, PaginationRequest, GroupType } from '@/types/group'
import { GroupTypeLabels } from '@/types/group'

// 响应式数据
const loading = ref(false)
const groups = ref<Group[]>([])
const searchQuery = ref('')
const formDialogOpen = ref(false)
const deleteDialogOpen = ref(false)
const selectedGroup = ref<Group | null>(null)

// 筛选条件
const filters = reactive({
  group_type: '',
  payment_institution: '',
  is_active: ''
})

// 分页信息
const pagination = reactive({
  total: 0,
  page: 1,
  page_size: 10,
  total_pages: 0
})

// 防抖搜索
const debouncedSearch = debounce(() => {
  pagination.page = 1
  loadGroups()
}, 300)

// 加载群组列表
const loadGroups = async () => {
  try {
    loading.value = true
    const params: PaginationRequest = {
      page: pagination.page,
      page_size: pagination.page_size,
      search: searchQuery.value || undefined,
      group_type: filters.group_type ? Number(filters.group_type) as GroupType : undefined,
      payment_institution: filters.payment_institution || undefined,
      is_active: filters.is_active ? filters.is_active === 'true' : undefined
    }

    const result = await GroupService.getGroupsWithPagination(params)
    groups.value = result.data
    Object.assign(pagination, result.pagination)
  } catch (error) {
    console.error('加载群组列表失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

// 切换页码
const changePage = (page: number) => {
  pagination.page = page
  loadGroups()
}

// 打开新增对话框
const openCreateDialog = () => {
  selectedGroup.value = null
  formDialogOpen.value = true
}

// 打开编辑对话框
const openEditDialog = (group: Group) => {
  selectedGroup.value = group
  formDialogOpen.value = true
}

// 打开删除对话框
const openDeleteDialog = (group: Group) => {
  selectedGroup.value = group
  deleteDialogOpen.value = true
}

// 表单提交成功处理
const handleFormSuccess = () => {
  formDialogOpen.value = false
  selectedGroup.value = null
  loadGroups()
}

// 删除成功处理
const handleDeleteSuccess = () => {
  deleteDialogOpen.value = false
  selectedGroup.value = null
  loadGroups()
}

// 获取群组类型标签
const getGroupTypeLabel = (type: GroupType): string => {
  return GroupTypeLabels[type] || '未知'
}

// 获取群组类型样式
const getGroupTypeClass = (type: GroupType): string => {
  const classes = {
    [1]: 'bg-blue-100 text-blue-800',
    [2]: 'bg-green-100 text-green-800',
    [3]: 'bg-purple-100 text-purple-800'
  }
  return classes[type] || 'bg-gray-100 text-gray-800'
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadGroups()
})
</script>

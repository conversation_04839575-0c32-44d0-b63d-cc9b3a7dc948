<template>
  <Dialog v-model:open="dialogOpen">
    <div class="space-y-6">
      <!-- 对话框标题 -->
      <div class="space-y-2">
        <h2 class="text-lg font-semibold">
          {{ isEdit ? '编辑群组' : '新增群组' }}
        </h2>
        <p class="text-sm text-muted-foreground">
          {{ isEdit ? '修改群组信息' : '创建新的Telegram群组配置' }}
        </p>
      </div>

      <!-- 表单 -->
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- 群组名称 -->
        <div class="space-y-2">
          <Label for="groupName">群组名称 <span class="text-red-500">*</span></Label>
          <Input
            id="groupName"
            v-model="form.group_name"
            placeholder="请输入群组名称"
            :class="{ 'border-red-500': errors.group_name }"
          />
          <p v-if="errors.group_name" class="text-sm text-red-500">{{ errors.group_name }}</p>
        </div>

        <!-- Telegram群组ID -->
        <div class="space-y-2">
          <Label for="telegramGroupId">Telegram群组ID <span class="text-red-500">*</span></Label>
          <Input
            id="telegramGroupId"
            v-model="form.telegram_group_id"
            placeholder="例如：-1001234567890"
            :disabled="isEdit"
            :class="{ 'border-red-500': errors.telegram_group_id }"
          />
          <p v-if="errors.telegram_group_id" class="text-sm text-red-500">{{ errors.telegram_group_id }}</p>
          <p v-if="isEdit" class="text-sm text-muted-foreground">编辑时不能修改Telegram群组ID</p>
        </div>

        <!-- 群组类型 -->
        <div class="space-y-2">
          <Label for="groupType">群组类型 <span class="text-red-500">*</span></Label>
          <select
            id="groupType"
            v-model="form.group_type"
            :class="[
              'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
              { 'border-red-500': errors.group_type }
            ]"
          >
            <option value="">请选择群组类型</option>
            <option value="1">商户群</option>
            <option value="2">供应商群</option>
            <option value="3">客服群</option>
          </select>
          <p v-if="errors.group_type" class="text-sm text-red-500">{{ errors.group_type }}</p>
        </div>

        <!-- 支付机构 -->
        <div class="space-y-2">
          <Label for="paymentInstitution">
            支付机构
            <span v-if="form.group_type === '2'" class="text-red-500">*</span>
          </Label>
          <Input
            id="paymentInstitution"
            v-model="form.payment_institution"
            placeholder="请输入支付机构名称"
            :class="{ 'border-red-500': errors.payment_institution }"
          />
          <p v-if="errors.payment_institution" class="text-sm text-red-500">{{ errors.payment_institution }}</p>
          <p v-if="form.group_type === '2'" class="text-sm text-muted-foreground">供应商群必须填写支付机构</p>
        </div>

        <!-- 状态（仅编辑时显示） -->
        <div v-if="isEdit" class="space-y-2">
          <Label for="isActive">状态</Label>
          <select
            id="isActive"
            v-model="form.is_active"
            class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <option value="true">启用</option>
            <option value="false">禁用</option>
          </select>
        </div>

        <!-- 按钮组 -->
        <div class="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" @click="handleCancel">
            取消
          </Button>
          <Button type="submit" :disabled="submitting">
            <div v-if="submitting" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {{ isEdit ? '更新中...' : '创建中...' }}
            </div>
            <span v-else>{{ isEdit ? '更新' : '创建' }}</span>
          </Button>
        </div>
      </form>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import Dialog from '@/components/ui/dialog.vue'
import Button from '@/components/ui/button.vue'
import Input from '@/components/ui/input.vue'
import Label from '@/components/ui/label.vue'
import { GroupService } from '@/services/groupService'
import type { Group, CreateGroupRequest, UpdateGroupRequest, GroupType } from '@/types/group'

interface Props {
  open: boolean
  group?: Group | null
}

interface Emits {
  'update:open': [value: boolean]
  success: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框开关状态
const dialogOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.group)

// 提交状态
const submitting = ref(false)

// 表单数据
const form = reactive({
  group_name: '',
  telegram_group_id: '',
  group_type: '',
  payment_institution: '',
  is_active: 'true'
})

// 表单错误
const errors = reactive({
  group_name: '',
  telegram_group_id: '',
  group_type: '',
  payment_institution: ''
})

// 重置表单
const resetForm = () => {
  form.group_name = ''
  form.telegram_group_id = ''
  form.group_type = ''
  form.payment_institution = ''
  form.is_active = 'true'
  
  // 清空错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
}

// 填充表单数据（编辑模式）
const fillForm = (group: Group) => {
  form.group_name = group.group_name
  form.telegram_group_id = group.telegram_group_id
  form.group_type = group.group_type.toString()
  form.payment_institution = group.payment_institution || ''
  form.is_active = group.is_active.toString()

  // 清空错误信息
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
}

// 表单验证
const validateForm = (): boolean => {
  let isValid = true

  // 清空之前的错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })

  // 群组名称验证
  if (!form.group_name.trim()) {
    errors.group_name = '请输入群组名称'
    isValid = false
  }

  // Telegram群组ID验证（仅在新增模式下验证）
  if (!isEdit.value) {
    if (!form.telegram_group_id.trim()) {
      errors.telegram_group_id = '请输入Telegram群组ID'
      isValid = false
    } else if (!form.telegram_group_id.match(/^-\d+$/)) {
      errors.telegram_group_id = 'Telegram群组ID格式不正确，应以-开头的数字'
      isValid = false
    }
  }

  // 群组类型验证
  if (!form.group_type) {
    errors.group_type = '请选择群组类型'
    isValid = false
  }

  // 供应商群必须填写支付机构
  if (form.group_type === '2' && !form.payment_institution.trim()) {
    errors.payment_institution = '供应商群必须填写支付机构'
    isValid = false
  }

  return isValid
}

// 处理表单提交
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    submitting.value = true

    if (isEdit.value && props.group) {
      // 更新群组
      const updateData: UpdateGroupRequest = {
        group_name: form.group_name,
        group_type: Number(form.group_type) as GroupType,
        payment_institution: form.payment_institution || undefined,
        is_active: form.is_active === 'true'
      }
      await GroupService.updateGroup(props.group.id, updateData)
    } else {
      // 创建群组
      const createData: CreateGroupRequest = {
        group_name: form.group_name,
        telegram_group_id: form.telegram_group_id,
        group_type: Number(form.group_type) as GroupType,
        payment_institution: form.payment_institution || undefined
      }
      await GroupService.createGroup(createData)
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    // 这里可以添加错误提示
  } finally {
    submitting.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('update:open', false)
}

// 监听对话框开关，重置表单
watch(() => props.open, (open: boolean) => {
  if (open) {
    if (props.group) {
      fillForm(props.group)
    } else {
      resetForm()
    }
  }
})
</script>

package services

import (
	"telegram-order-bot/internal/models"
	"testing"
	"time"
)

// TestMediaGroupCacheStructure 测试MediaGroupCache结构
func TestMediaGroupCacheStructure(t *testing.T) {
	cache := &MediaGroupCache{
		Messages:    []*MediaGroupMessage{},
		LastCaption: "TEST123",
		LastUpdate:  time.Now(),
		Processing:  false,
		Processed:   false,
	}

	if cache.Processing {
		t.Error("New cache should not be processing")
	}

	if cache.Processed {
		t.Error("New cache should not be processed")
	}

	// 模拟标记为处理中
	cache.Processing = true
	if !cache.Processing {
		t.Error("Cache should be marked as processing")
	}

	// 模拟处理完成
	cache.Processed = true
	cache.ProcessedAt = time.Now()
	cache.Processing = false

	if cache.Processing {
		t.Error("Cache should not be processing after completion")
	}

	if !cache.Processed {
		t.Error("Cache should be marked as processed")
	}
}

// TestForwardMediaGroupToCustomerService 测试多图相册转发功能
func TestForwardMediaGroupToCustomerService(t *testing.T) {

	// 创建模拟的多图相册消息
	mediaGroupMessages := []*MediaGroupMessage{
		{
			Message: &models.Message{
				MessageID:    1,
				MediaGroupID: "test_group_123",
				Photo: []models.PhotoSize{
					{FileID: "photo1_large", FileSize: 2000},
					{FileID: "photo1_small", FileSize: 1000},
				},
			},
		},
		{
			Message: &models.Message{
				MessageID:    2,
				MediaGroupID: "test_group_123",
				Photo: []models.PhotoSize{
					{FileID: "photo2_large", FileSize: 3000},
					{FileID: "photo2_small", FileSize: 1500},
				},
			},
		},
		{
			Message: &models.Message{
				MessageID:    3,
				MediaGroupID: "test_group_123",
				Caption:      "ORDER123456",
				Photo: []models.PhotoSize{
					{FileID: "photo3_large", FileSize: 1800},
					{FileID: "photo3_small", FileSize: 900},
				},
			},
		},
	}

	// 测试媒体组构建逻辑
	var mediaItems []MediaGroupItem
	for i, msgWrapper := range mediaGroupMessages {
		message := msgWrapper.Message
		if len(message.Photo) > 0 {
			// 选择最大尺寸的图片
			largestPhoto := message.Photo[0]
			for _, photo := range message.Photo {
				if photo.FileSize > largestPhoto.FileSize {
					largestPhoto = photo
				}
			}

			mediaItem := MediaGroupItem{
				Type:  "photo",
				Media: largestPhoto.FileID,
			}

			// 只在第一张图片上添加caption
			if i == 0 {
				mediaItem.Caption = "Test caption"
			}

			mediaItems = append(mediaItems, mediaItem)
		}
	}

	// 验证媒体项数量
	if len(mediaItems) != 3 {
		t.Errorf("Expected 3 media items, got %d", len(mediaItems))
	}

	// 验证选择了最大尺寸的图片
	expectedFileIDs := []string{"photo1_large", "photo2_large", "photo3_large"}
	for i, item := range mediaItems {
		if item.Media != expectedFileIDs[i] {
			t.Errorf("Expected media %d to be %s, got %s", i, expectedFileIDs[i], item.Media)
		}
		if item.Type != "photo" {
			t.Errorf("Expected media type to be 'photo', got %s", item.Type)
		}
	}

	// 验证只有第一张图片有caption
	if mediaItems[0].Caption != "Test caption" {
		t.Errorf("Expected first media item to have caption, got %s", mediaItems[0].Caption)
	}
	for i := 1; i < len(mediaItems); i++ {
		if mediaItems[i].Caption != "" {
			t.Errorf("Expected media item %d to have no caption, got %s", i, mediaItems[i].Caption)
		}
	}
}

// TestMediaGroupCacheCleanup 测试缓存清理机制
func TestMediaGroupCacheCleanup(t *testing.T) {
	// 创建一个BotService实例用于测试清理功能
	service := &BotService{}

	// 创建一个已处理的缓存项
	mediaGroupID := "test_cleanup_123"
	cache := &MediaGroupCache{
		Messages:    []*MediaGroupMessage{},
		Processed:   true,
		ProcessedAt: time.Now().Add(-31 * time.Minute), // 31分钟前处理完成
	}

	service.mediaGroups.Store(mediaGroupID, cache)

	// 执行清理
	service.cleanupProcessedMediaGroups()

	// 检查缓存是否被清理
	_, exists := service.mediaGroups.Load(mediaGroupID)
	if exists {
		t.Error("Expired media group cache should be cleaned up")
	}
}

package main

import (
	"log"
	"telegram-order-bot/internal/config"
	"telegram-order-bot/internal/database"
	"telegram-order-bot/internal/handlers"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using environment variables")
	}

	// 加载配置
	cfg := config.Load()

	// 初始化日志系统
	if err := logger.Init(&cfg.LogConfig); err != nil {
		panic("Failed to initialize logger: " + err.Error())
	}

	// 初始化数据库
	dbConfig := database.Config{
		Host:     cfg.DatabaseConfig.Host,
		Port:     cfg.DatabaseConfig.Port,
		User:     cfg.DatabaseConfig.User,
		Password: cfg.DatabaseConfig.Password,
		Database: cfg.DatabaseConfig.Database,
		Charset:  cfg.DatabaseConfig.Charset,
	}

	if err := database.InitDatabase(dbConfig); err != nil {
		panic("Failed to initialize database: " + err.Error())
	}

	// 自动迁移数据库表
	if err := database.AutoMigrate(); err != nil {
		panic("Failed to migrate database: " + err.Error())
	}

	// 初始化服务
	telegramService := services.NewTelegramService(cfg.TelegramBotToken)
	orderService := services.NewOrderService(cfg.ThirdPartyAPIConfig, cfg.QRCodeAPIConfig)
	qrService := services.NewQRCodeService()
	groupService := services.NewGroupService(database.GetDB())
	botService := services.NewBotService(telegramService, orderService, qrService, groupService, cfg)

	// 设置Gin路由
	r := gin.Default()

	// 健康检查端点
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// 数据库健康检查端点
	r.GET("/health/db", func(c *gin.Context) {
		if err := database.Ping(); err != nil {
			c.JSON(500, gin.H{"status": "error", "message": "Database connection failed"})
			return
		}
		c.JSON(200, gin.H{"status": "ok", "message": "Database connected"})
	})

	// Telegram webhook端点
	webhookHandler := handlers.NewWebhookHandler(botService)

	// 使用简单的webhook路径
	r.POST("/telegram-webhook", webhookHandler.HandleWebhook)

	// 群聊管理API
	groupHandler := handlers.NewGroupHandler(groupService)
	api := r.Group("/api/v1")
	{
		groups := api.Group("/groups")
		{
			groups.GET("", groupHandler.GetGroups)                         // 获取群聊列表
			groups.GET("/paginated", groupHandler.GetGroupsWithPagination) // 分页获取群聊列表
			groups.GET("/types", groupHandler.GetGroupTypes)               // 获取群聊类型
			groups.GET("/:telegram_group_id", groupHandler.GetGroup)       // 获取单个群聊
			groups.POST("", groupHandler.CreateGroup)                      // 创建群聊
			groups.PUT("/:id", groupHandler.UpdateGroup)                   // 更新群聊
			groups.DELETE("/:id", groupHandler.DeleteGroup)                // 删除群聊
		}
	}

	// 启动服务器
	logger.Infof("Starting server on port %s", cfg.Port)
	if err := r.Run(":" + cfg.Port); err != nil {
		logger.Fatalf("Failed to start server: %v", err)
	}
}

# 验证凭证超时设置功能

## 🎯 功能概述

为了防止二维码验证和订单查询请求长时间挂起，我们为以下API调用添加了30秒的超时设置：

1. **订单查询** (`QueryOrder`)
2. **二维码验证** (`VerifyVoucher`)

## 🔧 技术实现

### 超时机制

使用Go的 `context.WithTimeout` 实现：

```go
// 设置请求超时 (30秒)
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()
req = req.WithContext(ctx)
```

### 错误处理

当请求超时时，会返回明确的超时错误信息：

```go
// 检查是否是超时错误
if ctx.Err() == context.DeadlineExceeded {
    logger.WithField("timeout", "30s").Error("Request timed out")
    return nil, fmt.Errorf("Request timed out after 30 seconds")
}
```

## 📋 功能详情

### 1. 订单查询超时 (`QueryOrder`)

- **超时时间**: 30秒
- **触发条件**: 第三方订单API响应时间超过30秒
- **错误信息**: "Order query request timed out after 30 seconds"
- **日志记录**: 记录超时事件和相关订单ID

**代码位置**: `internal/services/order.go:120-127`

### 2. 二维码验证超时 (`VerifyVoucher`)

- **超时时间**: 30秒
- **触发条件**: 二维码验证API响应时间超过30秒
- **错误信息**: "QR code verification request timed out after 30 seconds"
- **日志记录**: 记录超时事件和二维码内容预览

**代码位置**: `internal/services/order.go:235-242`

## 📊 日志记录

### 超时日志示例

**订单查询超时**:
```
ERROR Order query request timed out timeout=30s merchant_order_id=ORDER123456
```

**二维码验证超时**:
```
ERROR QR code verification request timed out timeout=30s qr_content_preview=https://example.com/qr/...
```

### 详细请求日志

每个API调用都会记录：
- 请求开始时间
- 请求参数（敏感信息会被截断）
- 响应时间
- 响应内容
- 错误详情

## 🚀 使用场景

### 1. 网络不稳定
当网络连接不稳定时，避免请求无限期等待。

### 2. 第三方API故障
当第三方API服务出现故障或响应缓慢时，及时返回错误。

### 3. 系统资源保护
防止大量长时间等待的请求占用系统资源。

## 🔍 监控和调试

### 查看超时日志
```bash
# 查看所有超时相关日志
grep "timed out" /var/log/telegram-order-bot/app.log

# 查看订单查询超时
grep "Order query request timed out" /var/log/telegram-order-bot/app.log

# 查看二维码验证超时
grep "QR code verification request timed out" /var/log/telegram-order-bot/app.log
```

### 性能分析
通过日志可以分析：
- 超时频率
- 哪些API更容易超时
- 超时时间分布

## ⚙️ 配置说明

### 当前设置
- **超时时间**: 30秒（硬编码）
- **适用范围**: 订单查询和二维码验证

### 未来扩展
可以考虑将超时时间配置化：

```yaml
api:
  timeout: 30s
  order_query_timeout: 30s
  qr_verification_timeout: 30s
```

## 🧪 测试验证

### 测试文件
`internal/services/order_timeout_test.go` 包含以下测试：

1. **TestOrderQueryTimeout**: 测试订单查询超时
2. **TestQRCodeVerificationTimeout**: 测试二维码验证超时
3. **TestContextCancellation**: 测试上下文取消机制

### 运行测试
```bash
# 运行超时测试（注意：这些测试会实际等待30秒）
go test -v ./internal/services -run TestTimeout

# 快速编译测试
go test -c ./internal/services
```

## 📈 性能影响

### 正面影响
- ✅ 防止请求无限期挂起
- ✅ 提高系统响应性
- ✅ 更好的错误处理
- ✅ 资源使用优化

### 注意事项
- ⚠️ 30秒的超时时间需要根据实际API响应时间调整
- ⚠️ 超时可能导致正常但较慢的请求失败
- ⚠️ 需要监控超时频率以优化设置

## 🔄 错误处理流程

```
API请求 → 设置30秒超时 → 执行请求
    ↓
检查响应时间
    ↓
超过30秒? → 是 → 记录超时日志 → 返回超时错误
    ↓
    否 → 正常处理响应
```

---

**实现版本**: v2.2.1  
**实现日期**: 2025-01-17  
**超时设置**: 30秒  
**适用API**: 订单查询、二维码验证

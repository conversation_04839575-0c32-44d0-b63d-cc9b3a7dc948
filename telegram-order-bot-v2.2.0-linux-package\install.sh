#!/bin/bash

# Telegram Order Bot 安装脚本 v2.2.0
# 支持Media Group多图相册 + Telegram API群组名称获取
# 使用方法: sudo ./install.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
INSTALL_DIR="/opt/telegram-order-bot"
SERVICE_NAME="telegram-order-bot"
USER_NAME="telegram-bot"
GROUP_NAME="telegram-bot"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 修复locale
fix_locale() {
    export LC_ALL=C
    export LANG=C
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统
check_system() {
    log_info "检查系统环境..."
    
    # 检查是否为Linux
    if [[ "$(uname)" != "Linux" ]]; then
        log_error "此脚本只支持Linux系统"
        exit 1
    fi
    
    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        log_error "系统不支持systemd"
        exit 1
    fi
    
    # 显示系统信息
    log_info "系统信息:"
    log_info "  操作系统: $(uname -s)"
    log_info "  架构: $(uname -m)"
    log_info "  内核版本: $(uname -r)"
    
    log_info "系统检查通过"
}

# 检测架构并选择二进制文件
detect_architecture() {
    local arch=$(uname -m)
    local binary_name=""
    
    case $arch in
        x86_64|amd64)
            binary_name="telegram-order-bot-linux-amd64"
            log_info "检测到架构: x86_64/amd64"
            ;;
        aarch64|arm64)
            binary_name="telegram-order-bot-linux-arm64"
            log_info "检测到架构: aarch64/arm64"
            ;;
        armv7l|armv6l)
            log_error "暂不支持32位ARM架构: $arch"
            log_info "请使用64位系统"
            exit 1
            ;;
        *)
            log_error "不支持的架构: $arch"
            log_info "支持的架构: x86_64, aarch64"
            exit 1
            ;;
    esac
    
    if [[ ! -f "$binary_name" ]]; then
        log_error "找不到适合当前架构的二进制文件: $binary_name"
        log_info "可用的二进制文件:"
        ls -la telegram-order-bot-linux-* 2>/dev/null || log_error "没有找到任何二进制文件"
        exit 1
    fi
    
    echo "$binary_name"
}

# 创建用户和组
create_user() {
    log_info "创建用户和组..."
    
    # 创建组
    if ! getent group $GROUP_NAME > /dev/null 2>&1; then
        groupadd --system $GROUP_NAME
        log_info "创建组: $GROUP_NAME"
    else
        log_info "组已存在: $GROUP_NAME"
    fi
    
    # 创建用户
    if ! getent passwd $USER_NAME > /dev/null 2>&1; then
        useradd --system --gid $GROUP_NAME --home-dir $INSTALL_DIR --shell /bin/false $USER_NAME
        log_info "创建用户: $USER_NAME"
    else
        log_info "用户已存在: $USER_NAME"
    fi
}

# 创建安装目录
create_directories() {
    log_info "创建安装目录..."
    
    mkdir -p $INSTALL_DIR
    mkdir -p $INSTALL_DIR/logs
    
    log_info "安装目录: $INSTALL_DIR"
}

# 复制文件
copy_files() {
    log_info "复制程序文件..."
    
    # 检测架构并获取对应的二进制文件名
    local binary_name=$(detect_architecture)
    
    # 复制二进制文件
    cp "$binary_name" "$INSTALL_DIR/telegram-order-bot"
    chmod +x "$INSTALL_DIR/telegram-order-bot"
    log_info "复制二进制文件: $binary_name -> telegram-order-bot"
    
    # 检查并复制其他文件
    local optional_files=(".env.example" "README.md" "DEPLOYMENT.md" "get-group-id.sh" "start.sh")
    
    for file in "${optional_files[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$INSTALL_DIR/"
            if [[ "$file" == *.sh ]]; then
                chmod +x "$INSTALL_DIR/$file"
            fi
            log_debug "复制文件: $file"
        fi
    done
    
    log_info "文件复制完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    chown -R $USER_NAME:$GROUP_NAME $INSTALL_DIR
    chmod 755 $INSTALL_DIR
    chmod 755 $INSTALL_DIR/logs
    
    # 设置配置文件权限
    if [[ -f "$INSTALL_DIR/.env.example" ]]; then
        chmod 644 $INSTALL_DIR/.env.example
    fi
    
    log_info "权限设置完成"
}

# 创建systemd服务文件
create_service() {
    log_info "创建systemd服务文件..."
    
    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Telegram Order Bot Service v2.2.0
Documentation=file://$INSTALL_DIR/README.md
After=network-online.target
Wants=network-online.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Type=simple
User=$USER_NAME
Group=$GROUP_NAME
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/telegram-order-bot
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=30
TimeoutStopSec=30

# 输出和日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-order-bot

# 环境变量
Environment=GIN_MODE=release
Environment=LC_ALL=C
Environment=LANG=C

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR/logs
PrivateTmp=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictNamespaces=true
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    log_info "systemd服务创建完成"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    if [[ ! -f "$INSTALL_DIR/.env" ]]; then
        if [[ -f "$INSTALL_DIR/.env.example" ]]; then
            cp "$INSTALL_DIR/.env.example" "$INSTALL_DIR/.env"
            chown $USER_NAME:$GROUP_NAME "$INSTALL_DIR/.env"
            chmod 600 "$INSTALL_DIR/.env"
            log_warn "已创建 .env 配置文件，请编辑后启动服务"
        else
            log_warn "未找到 .env.example 文件，请手动创建配置文件"
        fi
    else
        log_info "配置文件已存在"
    fi
}

# 显示安装完成信息
show_completion() {
    echo
    log_info "=========================================="
    log_info "  安装完成！"
    log_info "=========================================="
    log_info "版本: v2.2.0"
    log_info "新功能: Media Group多图相册 + Telegram API群组名称"
    log_info "安装目录: $INSTALL_DIR"
    log_info "服务名称: $SERVICE_NAME"
    log_info "运行用户: $USER_NAME"
    log_info "系统架构: $(uname -m)"
    echo
    log_info "下一步操作:"
    log_info "1. 编辑配置文件: $INSTALL_DIR/.env"
    log_info "2. 启动服务: systemctl start $SERVICE_NAME"
    log_info "3. 查看状态: systemctl status $SERVICE_NAME"
    log_info "4. 查看日志: journalctl -u $SERVICE_NAME -f"
    echo
    log_info "配置帮助:"
    if [[ -f "$INSTALL_DIR/get-group-id.sh" ]]; then
        log_info "- 获取群组ID: $INSTALL_DIR/get-group-id.sh"
    fi
    if [[ -f "$INSTALL_DIR/README.md" ]]; then
        log_info "- 项目文档: $INSTALL_DIR/README.md"
    fi
    log_info "=========================================="
    echo
}

# 主函数
main() {
    log_info "Telegram Order Bot 安装程序 v2.2.0"
    log_info "新功能: Media Group多图相册 + Telegram API群组名称获取"
    log_info "支持架构: x86_64, aarch64"
    echo
    
    # 修复locale
    fix_locale
    
    check_root
    check_system
    create_user
    create_directories
    copy_files
    set_permissions
    create_service
    create_config
    show_completion
}

# 运行主函数
main "$@"

package models

import "time"

// TelegramUpdate Telegram webhook更新结构
type TelegramUpdate struct {
	UpdateID int      `json:"update_id"`
	Message  *Message `json:"message,omitempty"`
}

// Message Telegram消息结构
type Message struct {
	MessageID      int64       `json:"message_id"`
	From           *User       `json:"from,omitempty"`
	Chat           *Chat       `json:"chat,omitempty"`
	Date           int64       `json:"date"`
	Text           string      `json:"text,omitempty"`
	Caption        string      `json:"caption,omitempty"`
	Photo          []PhotoSize `json:"photo,omitempty"`
	ReplyToMessage *Message    `json:"reply_to_message,omitempty"`
	MediaGroupID   string      `json:"media_group_id,omitempty"` // Media Group ID for albums
}

// User Telegram用户结构
type User struct {
	ID        int64  `json:"id"`
	IsBot     bool   `json:"is_bot"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name,omitempty"`
	Username  string `json:"username,omitempty"`
}

// Chat Telegram聊天结构
type Chat struct {
	ID    int64  `json:"id"`
	Type  string `json:"type"`
	Title string `json:"title,omitempty"`
}

// PhotoSize 图片大小结构
type PhotoSize struct {
	FileID       string `json:"file_id"`
	FileUniqueID string `json:"file_unique_id"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	FileSize     int    `json:"file_size,omitempty"`
}

// OrderInfo 订单信息结构
type OrderInfo struct {
	OrderNumber  string   `json:"order_number"`   // 商户订单号
	HasImage     bool     `json:"has_image"`      // 是否包含图片
	ImageFileID  string   `json:"image_file_id"`  // 图片文件ID（单图片）
	IsMediaGroup bool     `json:"is_media_group"` // 是否为Media Group（多图相册）
	MediaGroupID string   `json:"media_group_id"` // Media Group ID
	ImageFileIDs []string `json:"image_file_ids"` // 多图片文件ID列表
}

// ThirdPartyOrderResponse 第三方API订单查询响应
type ThirdPartyOrderResponse struct {
	Success bool                 `json:"status"`
	Data    *ThirdPartyOrderData `json:"data,omitempty"`
	Message string               `json:"errMsg,omitempty"`
}

// ThirdPartyOrderData 第三方订单数据
type ThirdPartyOrderData struct {
	OrderID            string `json:"platform_order_no"` // 平台订单号
	SubmitOrderID      string `json:"submit_order_no"`   // 提交订单号
	MerchantOrderID    string `json:"mer_order_no"`      // 商户订单号
	PaymentInstitution string `json:"acq_name"`          // 支付机构
	Amount             string `json:"order_amount"`      // 金额（字符串类型）
	ActualAmount       string `json:"pay_amount"`        // 实付金额（字符串类型）
	PaymentCode        string `json:"acc_type"`          // 付款银行编码
	PaymentAccount     string `json:"acc_no"`            // 付款账号
	Status             string `json:"status"`            // 状态
}

type QRCodeScanResponse struct {
	Status  bool            `json:"status"`
	Message string          `json:"msg,omitempty"`
	Code    int32           `json:"code"` //code = 100 且 data.statusCode = 0000 凭证就是真
	Data    *QRCodeScanData `json:"data,omitempty"`
}

type QRCodeScanData struct {
	StatusCode    string                 `json:"statusCode"`
	StatusMessage string                 `json:"statusMessage,omitempty"`
	RqUID         string                 `json:"rqUID,omitempty"`
	KbankTxnId    string                 `json:"kbankTxnId,omitempty"`
	Data          *QRCodeTransactionData `json:"data,omitempty"` // 嵌套的实际交易数据

	// 为了向后兼容，保留直接字段（如果API有时直接返回）
	Amount    string `json:"amount,omitempty"`
	TransDate string `json:"transDate,omitempty"`
	TransTime string `json:"transTime,omitempty"`
	Sender    struct {
		Account struct {
			Value string `json:"value"`
		} `json:"account"`
	} `json:"sender,omitempty"`

	Receiver struct {
		Account struct {
			Value string `json:"value"`
		} `json:"account"`
	} `json:"receiver,omitempty"`
}

// QRCodeTransactionData 嵌套的交易数据结构
type QRCodeTransactionData struct {
	/* Language          string  `json:"language,omitempty"`
	TransRef          string  `json:"transRef,omitempty"`
	SendingBank       string  `json:"sendingBank,omitempty"`
	ReceivingBank     string  `json:"receivingBank,omitempty"`
	TransDate         string  `json:"transDate"`
	TransTime         string  `json:"transTime"` */
	Amount float64 `json:"amount"` // 注意：JSON中是数字类型
	/* PaidLocalAmount   float64 `json:"paidLocalAmount"`
	PaidLocalCurrency string  `json:"paidLocalCurrency"`
	CountryCode       string  `json:"countryCode"`
	TransFeeAmount    float64 `json:"transFeeAmount"`
	Ref1              string  `json:"ref1"`
	Ref2              string  `json:"ref2"`
	Ref3              string  `json:"ref3"`
	ToMerchantId      string  `json:"toMerchantId"` */

	Sender struct {
		/* DisplayName string `json:"displayName"`
		Name        string `json:"name"`
		Proxy       struct {
			Type  interface{} `json:"type"`  // 可能为null
			Value interface{} `json:"value"` // 可能为null
		} `json:"proxy"` */
		Account struct {
			Type  string `json:"type"`
			Value string `json:"value"`
		} `json:"account"`
	} `json:"sender"`

	Receiver struct {
		/* DisplayName string `json:"displayName"`
		Name        string `json:"name"`
		Proxy       struct {
			Type  string `json:"type"`
			Value string `json:"value"`
		} `json:"proxy"` */
		Account struct {
			Type  string `json:"type"`
			Value string `json:"value"`
		} `json:"account"`
	} `json:"receiver"`
}

// QRCodeInfo 二维码信息
type QRCodeInfo struct {
	Found   bool   `json:"found"`   // 是否找到二维码
	Content string `json:"content"` // 二维码内容
}

// OrderProcessingState 订单处理状态
type OrderProcessingState struct {
	OrderNumber     string    `json:"order_number"`
	ChatID          int64     `json:"chat_id"`
	MessageID       int64     `json:"message_id"`
	Status          string    `json:"status"` // processing, completed, failed
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	SubmitOrderID   string    `json:"submit_order_id,omitempty"`
	SupplierGroupID string    `json:"supplier_group_id,omitempty"`
}

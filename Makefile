.PHONY: build run test clean docker-build docker-run setup-webhook

# Variable definitions
APP_NAME=telegram-order-bot
DOCKER_IMAGE=$(APP_NAME):latest
PORT=8080

# Build application
build:
	go build -o bin/$(APP_NAME) .

# Run application
run:
	go run main.go

# Run tests
test:
	go test -v ./...

# Clean build files
clean:
	rm -rf bin/
	docker rmi $(DOCKER_IMAGE) 2>/dev/null || true

# Install dependencies
deps:
	go mod tidy
	go mod download

# Format code
fmt:
	go fmt ./...

# Code linting
lint:
	golangci-lint run

# Docker build
docker-build:
	docker build -t $(DOCKER_IMAGE) .

# Docker run
docker-run: docker-build
	docker run -p $(PORT):$(PORT) --env-file .env $(DOCKER_IMAGE)

# Run with docker-compose
compose-up:
	docker-compose up -d

# Stop docker-compose
compose-down:
	docker-compose down

# View docker-compose logs
compose-logs:
	docker-compose logs -f

# 设置Telegram Webhook (需要设置BOT_TOKEN和WEBHOOK_URL环境变量)
setup-webhook:
	@if [ -z "$(BOT_TOKEN)" ]; then echo "请设置BOT_TOKEN环境变量"; exit 1; fi
	@if [ -z "$(WEBHOOK_URL)" ]; then echo "请设置WEBHOOK_URL环境变量"; exit 1; fi
	curl -X POST "https://api.telegram.org/bot$(BOT_TOKEN)/setWebhook" \
		-H "Content-Type: application/json" \
		-d '{"url": "$(WEBHOOK_URL)/webhook/$(BOT_TOKEN)"}'

# 删除Webhook
delete-webhook:
	@if [ -z "$(BOT_TOKEN)" ]; then echo "请设置BOT_TOKEN环境变量"; exit 1; fi
	curl -X POST "https://api.telegram.org/bot$(BOT_TOKEN)/deleteWebhook"

# 获取Webhook信息
get-webhook:
	@if [ -z "$(BOT_TOKEN)" ]; then echo "请设置BOT_TOKEN环境变量"; exit 1; fi
	curl "https://api.telegram.org/bot$(BOT_TOKEN)/getWebhookInfo"

# 开发环境设置
dev-setup:
	@echo "设置开发环境..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "已创建.env文件，请编辑配置"; fi
	go mod tidy
	@echo "开发环境设置完成！"
	@echo "请编辑.env文件配置必要的环境变量，然后运行 'make run'"

# 生产环境部署
deploy:
	@echo "部署到生产环境..."
	docker-compose down
	docker-compose pull
	docker-compose up -d
	@echo "部署完成！"

# 健康检查
health:
	curl -f http://localhost:$(PORT)/health || exit 1

# 配置检查
check-config:
	@chmod +x scripts/check-config.sh
	@./scripts/check-config.sh

# 帮助信息
help:
	@echo "可用的命令："
	@echo "  build         - 构建应用"
	@echo "  run           - 运行应用"
	@echo "  test          - 运行测试"
	@echo "  clean         - 清理构建文件"
	@echo "  deps          - 安装依赖"
	@echo "  fmt           - 格式化代码"
	@echo "  lint          - 代码检查"
	@echo "  docker-build  - 构建Docker镜像"
	@echo "  docker-run    - 运行Docker容器"
	@echo "  compose-up    - 使用docker-compose启动"
	@echo "  compose-down  - 停止docker-compose"
	@echo "  compose-logs  - 查看docker-compose日志"
	@echo "  setup-webhook - 设置Telegram Webhook"
	@echo "  delete-webhook- 删除Telegram Webhook"
	@echo "  get-webhook   - 获取Webhook信息"
	@echo "  dev-setup     - 设置开发环境"
	@echo "  deploy        - 部署到生产环境"
	@echo "  health        - 健康检查"
	@echo "  check-config  - 检查配置是否正确"
	@echo "  help          - 显示帮助信息"

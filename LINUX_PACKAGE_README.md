# Telegram Order Bot Linux 部署包 v2.2.1

## 📦 包内容

本Linux部署包包含以下文件：

```
telegram-order-bot-v2.2.1-linux.zip
├── telegram-order-bot-linux      # Linux二进制可执行文件 (14MB)
├── .env.example                   # 环境变量配置示例
├── telegram-order-bot.service    # systemd服务配置文件
├── install.sh                    # 自动安装脚本
├── start.sh                      # 启动脚本
├── get-group-id.sh               # 获取群组ID工具脚本
├── README.md                     # 详细使用说明
├── DEPLOYMENT.md                 # 部署指南
└── VERSION.md                    # 版本更新日志
```

## 🚀 快速部署

### 1. 下载并解压
```bash
# 下载包文件
wget telegram-order-bot-v2.2.1-linux.zip

# 解压
unzip telegram-order-bot-v2.2.1-linux.zip
cd telegram-order-bot-linux-package
```

### 2. 运行安装脚本
```bash
# 给脚本执行权限
chmod +x install.sh

# 运行安装（需要root权限）
sudo ./install.sh
```

### 3. 配置环境变量
```bash
# 复制配置文件
sudo cp .env.example /opt/telegram-order-bot/.env

# 编辑配置
sudo nano /opt/telegram-order-bot/.env
```

### 4. 启动服务
```bash
# 启动服务
sudo systemctl start telegram-order-bot

# 设置开机自启
sudo systemctl enable telegram-order-bot

# 查看状态
sudo systemctl status telegram-order-bot
```

## 🔧 新功能特性 (v2.2.1)

### 📸 多图相册批量转发
- 失败的多图相册订单现在会将所有图片一起转发到客服群
- 使用Telegram的sendMediaGroup API实现批量发送
- 智能回退机制：批量发送失败时自动切换到逐张发送

### 🔍 详细订单查询日志
- 完整记录API请求和响应数据
- 结构化记录所有订单字段信息
- 增强错误追踪和调试支持

### 🚀 多图相册处理优化
- 解决重复处理和重复转发问题
- 智能延迟处理机制
- 自动缓存清理和内存管理

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- **架构**: x86_64 (AMD64)
- **内存**: 最少 256MB RAM
- **磁盘**: 最少 100MB 可用空间
- **网络**: 需要访问Telegram API和第三方订单API

## 🔧 手动部署

如果不想使用自动安装脚本，可以手动部署：

```bash
# 1. 创建用户和目录
sudo useradd -r -s /bin/false telegram-bot
sudo mkdir -p /opt/telegram-order-bot
sudo mkdir -p /var/log/telegram-order-bot

# 2. 复制文件
sudo cp telegram-order-bot-linux /opt/telegram-order-bot/
sudo cp .env.example /opt/telegram-order-bot/.env
sudo chmod +x /opt/telegram-order-bot/telegram-order-bot-linux

# 3. 设置权限
sudo chown -R telegram-bot:telegram-bot /opt/telegram-order-bot
sudo chown -R telegram-bot:telegram-bot /var/log/telegram-order-bot

# 4. 安装systemd服务
sudo cp telegram-order-bot.service /etc/systemd/system/
sudo systemctl daemon-reload
```

## 📊 监控和日志

```bash
# 查看服务状态
sudo systemctl status telegram-order-bot

# 查看实时日志
sudo journalctl -u telegram-order-bot -f

# 查看应用日志
sudo tail -f /var/log/telegram-order-bot/app.log
```

## 🔄 更新升级

```bash
# 停止服务
sudo systemctl stop telegram-order-bot

# 备份当前版本
sudo cp /opt/telegram-order-bot/telegram-order-bot-linux /opt/telegram-order-bot/telegram-order-bot-linux.backup

# 替换新版本
sudo cp telegram-order-bot-linux /opt/telegram-order-bot/

# 重启服务
sudo systemctl start telegram-order-bot
```

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   sudo journalctl -u telegram-order-bot --no-pager
   ```

2. **权限问题**
   ```bash
   sudo chown -R telegram-bot:telegram-bot /opt/telegram-order-bot
   ```

3. **端口占用**
   ```bash
   sudo netstat -tlnp | grep :8080
   ```

## 📞 技术支持

- 查看 `README.md` 获取详细配置说明
- 查看 `DEPLOYMENT.md` 获取部署指南
- 查看 `VERSION.md` 了解版本更新内容

---

**版本**: v2.2.1  
**发布日期**: 2025-01-17  
**包大小**: ~7.5MB  
**二进制大小**: ~14MB

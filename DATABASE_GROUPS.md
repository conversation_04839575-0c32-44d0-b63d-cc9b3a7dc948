# 数据库群聊管理功能

## 🎯 功能概述

机器人现在支持从数据库获取群聊信息，不再依赖配置文件。这提供了更灵活的群聊管理方式。

## 📋 功能特性

### 🔧 核心功能
- **数据库存储**: 群聊信息存储在MySQL数据库中
- **动态管理**: 通过API接口动态添加、修改、删除群聊
- **类型分类**: 支持商户群、供应商群、客服群三种类型
- **支付机构**: 供应商群可以关联特定的支付机构
- **状态管理**: 支持启用/禁用群聊状态

### 🏗️ 数据库结构

#### 群聊表 (`groups`)
```sql
CREATE TABLE `groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL COMMENT '群聊名称',
  `telegram_group_id` varchar(50) NOT NULL COMMENT 'Telegram群组ID',
  `payment_institution` varchar(255) DEFAULT NULL COMMENT '支付机构',
  `group_type` tinyint NOT NULL COMMENT '群聊类型(1:商户群,2:供应商群,3:客服群)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_telegram_group_id` (`telegram_group_id`)
);
```

#### 群聊类型说明
- **1**: 商户群 - 处理订单消息
- **2**: 供应商群 - 处理失败订单转发
- **3**: 客服群 - 人工客服处理

## 🚀 部署配置

### 1. 数据库配置

在 `.env` 文件中添加数据库配置：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=telegram_order_bot
DB_CHARSET=utf8mb4
```

### 2. 初始化数据库

```bash
# 1. 创建数据库
mysql -u root -p < database/init.sql

# 2. 启动应用（会自动执行数据库迁移）
./telegram-order-bot-linux
```

### 3. 验证数据库连接

```bash
# 检查数据库连接状态
curl http://localhost:8080/health/db
```

## 📡 API接口

### 基础URL
```
http://localhost:8080/api/v1
```

### 1. 获取群聊列表
```bash
# 获取所有商户群
GET /groups?type=1

# 获取所有供应商群
GET /groups?type=2

# 获取特定支付机构的供应商群
GET /groups?type=2&payment_institution=支付宝

# 获取所有客服群
GET /groups?type=3
```

### 2. 获取单个群聊
```bash
GET /groups/{telegram_group_id}
```

### 3. 创建群聊
```bash
POST /groups
Content-Type: application/json

{
  "group_name": "测试商户群",
  "telegram_group_id": "-1001234567890",
  "payment_institution": "支付宝",  // 供应商群必填
  "group_type": 1  // 1=商户群, 2=供应商群, 3=客服群
}
```

### 4. 更新群聊
```bash
PUT /groups/{id}
Content-Type: application/json

{
  "group_name": "更新后的群名",
  "payment_institution": "微信支付",
  "is_active": true
}
```

### 5. 删除群聊
```bash
DELETE /groups/{id}
```

### 6. 获取群聊类型
```bash
GET /groups/types
```

## 🔄 兼容性

### 配置文件回退
如果数据库中没有找到群聊信息，系统会自动回退到配置文件模式，确保向后兼容。

### 缓存机制
- 群聊类型和名称会被缓存在内存中
- 首次查询后会缓存结果，提高性能
- 支持缓存刷新机制

## 📊 使用示例

### 1. 添加商户群
```bash
curl -X POST http://localhost:8080/api/v1/groups \
  -H "Content-Type: application/json" \
  -d '{
    "group_name": "ABC商户群",
    "telegram_group_id": "-1001234567890",
    "group_type": 1
  }'
```

### 2. 添加供应商群
```bash
curl -X POST http://localhost:8080/api/v1/groups \
  -H "Content-Type: application/json" \
  -d '{
    "group_name": "支付宝供应商群",
    "telegram_group_id": "-1001234567891",
    "payment_institution": "支付宝",
    "group_type": 2
  }'
```

### 3. 添加客服群
```bash
curl -X POST http://localhost:8080/api/v1/groups \
  -H "Content-Type: application/json" \
  -d '{
    "group_name": "人工客服群",
    "telegram_group_id": "-1001234567892",
    "group_type": 3
  }'
```

### 4. 查询群聊
```bash
# 查看所有商户群
curl "http://localhost:8080/api/v1/groups?type=1"

# 查看支付宝相关的供应商群
curl "http://localhost:8080/api/v1/groups?type=2&payment_institution=支付宝"
```

## 🔧 技术实现

### 数据库层
- **GORM v2**: ORM框架
- **MySQL**: 数据存储
- **连接池**: 优化数据库连接性能
- **自动迁移**: 启动时自动创建/更新表结构

### 服务层
- **GroupService**: 群聊业务逻辑
- **缓存机制**: 内存缓存提高查询性能
- **错误处理**: 完善的错误处理和日志记录

### API层
- **RESTful API**: 标准的REST接口
- **参数验证**: 请求参数自动验证
- **JSON响应**: 统一的JSON响应格式

## 🚨 注意事项

1. **Telegram群组ID格式**: 必须是完整的群组ID（如：-1001234567890）
2. **群聊类型**: 必须是1、2、3中的一个
3. **支付机构**: 供应商群建议填写支付机构信息
4. **数据库权限**: 确保数据库用户有足够的权限
5. **备份**: 定期备份群聊配置数据

## 📈 性能优化

- **索引优化**: 在关键字段上创建索引
- **连接池**: 配置合适的数据库连接池大小
- **缓存策略**: 合理使用内存缓存减少数据库查询
- **批量操作**: 支持批量查询和更新操作

---

**版本**: v2.2.1+  
**数据库**: MySQL 5.7+  
**ORM**: GORM v2  
**API**: RESTful JSON API

#!/bin/bash

# Generate Supplier Groups Configuration Script

echo "🔧 Supplier Groups Configuration Generator"
echo "=========================================="

# Check if supplier-groups.json exists
if [[ ! -f "supplier-groups.json" ]]; then
    echo "❌ supplier-groups.json not found"
    echo "Please create supplier-groups.json first with your supplier configuration"
    exit 1
fi

echo "📋 Reading supplier-groups.json..."

# Validate JSON
if ! python3 -m json.tool supplier-groups.json > /dev/null 2>&1; then
    echo "❌ Invalid JSON format in supplier-groups.json"
    exit 1
fi

echo "✅ JSON format is valid"

# Generate minified JSON for .env
echo "🔄 Generating minified JSON for .env..."
minified_json=$(python3 -c "import json; print(json.dumps(json.load(open('supplier-groups.json')), separators=(',', ':')))")

echo ""
echo "📝 Add this line to your .env file:"
echo "=================================="
echo "SUPPLIER_GROUPS_JSON=$minified_json"
echo ""

# Show formatted version for verification
echo "📊 Formatted configuration (for verification):"
echo "=============================================="
python3 -m json.tool supplier-groups.json

echo ""
echo "✅ Configuration generated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Copy the SUPPLIER_GROUPS_JSON line above to your .env file"
echo "2. Update the group_id values with your actual Telegram group IDs"
echo "3. Restart your bot service"

#!/bin/bash

# Telegram Order Bot Service Management Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SERVICE_NAME="telegram-order-bot"
INSTALL_DIR="/opt/telegram-order-bot"

# Function to show usage
show_usage() {
    echo -e "${BLUE}🤖 Telegram Order Bot Service Manager${NC}"
    echo "Usage: $0 {start|stop|restart|status|enable|disable|logs|update}"
    echo ""
    echo "Commands:"
    echo "  start    - Start the service"
    echo "  stop     - Stop the service"
    echo "  restart  - Restart the service"
    echo "  status   - Show service status"
    echo "  enable   - Enable service to start on boot"
    echo "  disable  - Disable service from starting on boot"
    echo "  logs     - Show service logs (follow mode)"
    echo "  update   - Update the binary (requires new binary in current directory)"
}

# Function to check if running as root (for some operations)
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}❌ This operation requires root privileges${NC}"
        exit 1
    fi
}

# Function to update binary
update_binary() {
    check_root
    
    if [[ ! -f "./telegram-order-bot-linux" ]]; then
        echo -e "${RED}❌ Binary file telegram-order-bot-linux not found in current directory${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🔄 Updating binary...${NC}"
    
    # Stop service
    systemctl stop ${SERVICE_NAME}
    
    # Backup old binary
    cp ${INSTALL_DIR}/telegram-order-bot-linux ${INSTALL_DIR}/telegram-order-bot-linux.backup
    
    # Copy new binary
    cp ./telegram-order-bot-linux ${INSTALL_DIR}/
    chmod +x ${INSTALL_DIR}/telegram-order-bot-linux
    chown telegram-bot:telegram-bot ${INSTALL_DIR}/telegram-order-bot-linux
    
    # Start service
    systemctl start ${SERVICE_NAME}
    
    echo -e "${GREEN}✅ Binary updated and service restarted${NC}"
}

case "$1" in
    start)
        echo -e "${BLUE}🚀 Starting ${SERVICE_NAME}...${NC}"
        sudo systemctl start ${SERVICE_NAME}
        echo -e "${GREEN}✅ Service started${NC}"
        ;;
    stop)
        echo -e "${BLUE}🛑 Stopping ${SERVICE_NAME}...${NC}"
        sudo systemctl stop ${SERVICE_NAME}
        echo -e "${GREEN}✅ Service stopped${NC}"
        ;;
    restart)
        echo -e "${BLUE}🔄 Restarting ${SERVICE_NAME}...${NC}"
        sudo systemctl restart ${SERVICE_NAME}
        echo -e "${GREEN}✅ Service restarted${NC}"
        ;;
    status)
        echo -e "${BLUE}📊 Service Status:${NC}"
        systemctl status ${SERVICE_NAME}
        ;;
    enable)
        echo -e "${BLUE}⚡ Enabling ${SERVICE_NAME} to start on boot...${NC}"
        sudo systemctl enable ${SERVICE_NAME}
        echo -e "${GREEN}✅ Service enabled${NC}"
        ;;
    disable)
        echo -e "${BLUE}🚫 Disabling ${SERVICE_NAME} from starting on boot...${NC}"
        sudo systemctl disable ${SERVICE_NAME}
        echo -e "${GREEN}✅ Service disabled${NC}"
        ;;
    logs)
        echo -e "${BLUE}📋 Showing logs for ${SERVICE_NAME} (Press Ctrl+C to exit):${NC}"
        journalctl -u ${SERVICE_NAME} -f
        ;;
    update)
        update_binary
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

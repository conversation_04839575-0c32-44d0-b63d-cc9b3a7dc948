@echo off
chcp 65001 >nul

echo 🚀 群组管理前端系统安装脚本
echo ================================

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js (版本 ^>= 16^)
    pause
    exit /b 1
)

REM 检查npm是否安装
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i

echo ✅ Node.js 版本: %NODE_VERSION%
echo ✅ npm 版本: %NPM_VERSION%
echo.

REM 安装依赖
echo 📦 安装项目依赖...
npm install

if %errorlevel% equ 0 (
    echo ✅ 依赖安装成功！
) else (
    echo ❌ 依赖安装失败，请检查网络连接或尝试使用 yarn
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.
echo 📋 可用命令：
echo   npm run dev     - 启动开发服务器
echo   npm run build   - 构建生产版本
echo   npm run preview - 预览生产版本
echo.
echo 🌐 开发服务器将在 http://localhost:3000 启动
echo 🔗 确保后端服务运行在 http://localhost:8080
echo.
echo 现在可以运行: npm run dev
pause

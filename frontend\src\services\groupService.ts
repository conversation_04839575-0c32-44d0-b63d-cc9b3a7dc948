import api from './api'
import type {
  Group,
  CreateGroupRequest,
  UpdateGroupRequest,
  PaginationRequest,
  GroupListResponse,
  GroupDetailResponse,
  GroupPaginationResponse,
  ApiResponse
} from '@/types/group'

export class GroupService {
  // 获取群组列表（不分页）
  static async getGroups(params?: {
    type?: number
    payment_institution?: string
  }): Promise<Group[]> {
    const response = await api.get<GroupListResponse>('/groups', { params })
    if (response.data.success && response.data.data) {
      return response.data.data
    }
    throw new Error(response.data.message || '获取群组列表失败')
  }

  // 获取群组列表（分页）
  static async getGroupsWithPagination(params: PaginationRequest): Promise<{
    data: Group[]
    pagination: {
      total: number
      page: number
      page_size: number
      total_pages: number
    }
  }> {
    const response = await api.get<GroupPaginationResponse>('/groups/paginated', { 
      params: {
        page: params.page || 1,
        page_size: params.page_size || 10,
        group_type: params.group_type,
        payment_institution: params.payment_institution,
        is_active: params.is_active,
        search: params.search
      }
    })
    
    if (response.data.success && response.data.data && response.data.pagination) {
      return {
        data: response.data.data,
        pagination: response.data.pagination
      }
    }
    throw new Error(response.data.message || '获取群组列表失败')
  }

  // 获取单个群组
  static async getGroup(telegramGroupId: string): Promise<Group> {
    const response = await api.get<GroupDetailResponse>(`/groups/${telegramGroupId}`)
    if (response.data.success && response.data.data) {
      return response.data.data
    }
    throw new Error(response.data.message || '获取群组详情失败')
  }

  // 创建群组
  static async createGroup(data: CreateGroupRequest): Promise<Group> {
    const response = await api.post<GroupDetailResponse>('/groups', data)
    if (response.data.success && response.data.data) {
      return response.data.data
    }
    throw new Error(response.data.message || '创建群组失败')
  }

  // 更新群组
  static async updateGroup(id: number, data: UpdateGroupRequest): Promise<Group> {
    const response = await api.put<GroupDetailResponse>(`/groups/${id}`, data)
    if (response.data.success && response.data.data) {
      return response.data.data
    }
    throw new Error(response.data.message || '更新群组失败')
  }

  // 删除群组（软删除）
  static async deleteGroup(id: number): Promise<void> {
    const response = await api.delete<ApiResponse>(`/groups/${id}`)
    if (!response.data.success) {
      throw new Error(response.data.message || '删除群组失败')
    }
  }

  // 获取群组类型列表
  static async getGroupTypes(): Promise<Array<{ value: number; label: string }>> {
    // 这里可以从后端获取，或者直接返回静态数据
    return [
      { value: 1, label: '商户群' },
      { value: 2, label: '供应商群' },
      { value: 3, label: '客服群' }
    ]
  }
}

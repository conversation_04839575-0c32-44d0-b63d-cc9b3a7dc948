# 快速启动指南

## 🚀 5分钟快速开始

### 1. 检查环境
确保您的系统已安装：
- Node.js >= 16.0.0
- npm >= 8.0.0

```bash
node --version
npm --version
```

### 2. 安装项目

#### 选项A: 使用安装脚本（推荐）
```bash
# Linux/macOS
chmod +x setup.sh
./setup.sh

# Windows
setup.bat
```

#### 选项B: 手动安装
```bash
# 1. 检查项目结构
node test-install.js

# 2. 测试配置文件
node test-config.js

# 3. 安装依赖
npm install

# 4. 启动开发服务器
npm run dev
```

### 3. 访问应用
打开浏览器访问：`http://localhost:3000`

### 4. 后端配置
确保后端服务运行在：`http://localhost:8080`

## 🔧 常见问题

### Q: PostCSS 配置错误
**A:** 如果遇到 "module is not defined in ES module scope" 错误：
```bash
# 检查配置文件
node test-config.js

# 确保 package.json 中没有 "type": "module"
# 确保配置文件使用 CommonJS 语法 (module.exports)
```

### Q: npm install 失败
**A:** 尝试以下解决方案：
```bash
# 清除缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# 或使用 yarn
npm install -g yarn
yarn install
```

### Q: 端口冲突
**A:** 修改 `vite.config.ts` 中的端口：
```typescript
server: {
  port: 3001, // 改为其他端口
}
```

### Q: API 请求失败
**A:** 检查后端服务：
1. 确保后端运行在 `http://localhost:8080`
2. 检查 API 路径是否正确
3. 查看浏览器控制台错误信息

### Q: 样式不显示
**A:** 检查 Tailwind CSS 配置：
```bash
# 重新构建样式
npm run build
npm run dev
```

## 📁 项目结构说明

```
frontend/
├── src/
│   ├── components/ui/      # 基础UI组件
│   ├── components/         # 业务组件
│   ├── views/             # 页面组件
│   ├── services/          # API服务
│   ├── types/             # 类型定义
│   └── lib/               # 工具函数
├── public/                # 静态资源
└── 配置文件
```

## 🎯 功能测试

启动应用后，您可以测试以下功能：

1. **查看群组列表** - 主页面显示群组表格
2. **搜索群组** - 在搜索框输入群组名称
3. **筛选群组** - 按类型、支付机构、状态筛选
4. **新增群组** - 点击"新增群组"按钮
5. **编辑群组** - 点击表格中的编辑按钮
6. **删除群组** - 点击表格中的删除按钮

## 📞 获取帮助

如果遇到问题：
1. 查看 `README.md` 详细文档
2. 查看 `DEPLOYMENT.md` 部署指南
3. 检查浏览器控制台错误信息
4. 检查后端服务日志

## 🔄 下一步

- 配置后端API连接
- 自定义样式和主题
- 添加更多功能模块
- 部署到生产环境

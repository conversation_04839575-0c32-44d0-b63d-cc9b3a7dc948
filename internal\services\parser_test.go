package services

import (
	"telegram-order-bot/internal/config"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"
	"testing"
)

func init() {
	// 初始化logger用于测试
	logConfig := &config.LogConfig{
		Level:      "debug",
		FilePath:   "",
		MaxSize:    10,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   false,
		Console:    true,
	}
	logger.Init(logConfig)
}

func TestMessageParser_IsOrderMessage(t *testing.T) {
	parser := NewMessageParser()

	tests := []struct {
		name     string
		text     string
		expected bool
	}{
		{
			name: "Valid order message",
			text: `Order Number: NO123456
Payer Name: MR.Demo
Bank Account: ***********
Date Time: 2024-07-18 12:32
Amount: 2000`,
			expected: true,
		},
		{
			name: "Valid order message with case insensitive",
			text: `order number: NO123456
payer name: <PERSON>.Demo`,
			expected: true,
		},
		{
			name: "Invalid message without order number",
			text: `Payer Name: MR.Demo
Bank Account: ***********`,
			expected: false,
		},
		{
			name:     "Empty message",
			text:     "",
			expected: false,
		},
		{
			name:     "Random text",
			text:     "Hello, how are you?",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建图文消息，使用caption字段
			message := &models.Message{
				Caption: tt.text,
				Photo: []models.PhotoSize{
					{FileID: "test_file_id", FileSize: 1000},
				},
			}
			result := parser.IsOrderMessage(message)
			if result != tt.expected {
				t.Errorf("IsOrderMessage() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMessageParser_ParseOrderInfo(t *testing.T) {
	parser := NewMessageParser()

	caption := `Order Number: NO123456
Payer Name: MR.Demo
Bank Account: ***********
Date Time: 2024-07-18 12:32
Amount: 2000`

	message := &models.Message{
		Caption: caption,
		Photo: []models.PhotoSize{
			{FileID: "test_file_id", FileSize: 1000},
		},
	}

	orderInfo := parser.ParseOrderInfo(message)

	if orderInfo == nil {
		t.Fatal("ParseOrderInfo() returned nil")
	}

	if orderInfo.OrderNumber != "NO123456" {
		t.Errorf("OrderNumber = %v, want %v", orderInfo.OrderNumber, "NO123456")
	}

	if !orderInfo.HasImage {
		t.Error("HasImage should be true")
	}

	if orderInfo.ImageFileID != "test_file_id" {
		t.Errorf("ImageFileID = %v, want %v", orderInfo.ImageFileID, "test_file_id")
	}

}

func TestMessageParser_IsMediaGroupOrderMessage(t *testing.T) {
	parser := NewMessageParser()

	tests := []struct {
		name     string
		caption  string
		expected bool
	}{
		{
			name:     "Valid single line order number",
			caption:  "NO123456",
			expected: true,
		},
		{
			name:     "Valid alphanumeric order number",
			caption:  "ORDER789ABC",
			expected: true,
		},
		{
			name:     "Invalid multi-line caption",
			caption:  "NO123456\nExtra info",
			expected: false,
		},
		{
			name:     "Invalid empty caption",
			caption:  "",
			expected: false,
		},
		{
			name:     "Invalid caption with spaces",
			caption:  "NO123456 extra",
			expected: false,
		},
		{
			name:     "Valid with whitespace trimming",
			caption:  "  NO123456  ",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parser.IsMediaGroupOrderMessage(tt.caption)
			if result != tt.expected {
				t.Errorf("IsMediaGroupOrderMessage() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMessageParser_ParseMediaGroupOrderInfo(t *testing.T) {
	parser := NewMessageParser()

	// 创建模拟的Media Group消息
	messages := []*MediaGroupMessage{
		{
			Message: &models.Message{
				MessageID:    1,
				MediaGroupID: "test_group_123",
				Photo: []models.PhotoSize{
					{FileID: "image1_small", FileSize: 1000},
					{FileID: "image1_large", FileSize: 2000},
				},
			},
		},
		{
			Message: &models.Message{
				MessageID:    2,
				MediaGroupID: "test_group_123",
				Photo: []models.PhotoSize{
					{FileID: "image2_small", FileSize: 1500},
					{FileID: "image2_large", FileSize: 3000},
				},
			},
		},
		{
			Message: &models.Message{
				MessageID:    3,
				MediaGroupID: "test_group_123",
				Caption:      "NO123456",
				Photo: []models.PhotoSize{
					{FileID: "image3_small", FileSize: 800},
					{FileID: "image3_large", FileSize: 1800},
				},
			},
		},
	}

	orderInfo := parser.ParseMediaGroupOrderInfo(messages, "NO123456")

	if orderInfo == nil {
		t.Fatal("ParseMediaGroupOrderInfo() returned nil")
	}

	if orderInfo.OrderNumber != "NO123456" {
		t.Errorf("OrderNumber = %v, want %v", orderInfo.OrderNumber, "NO123456")
	}

	if !orderInfo.IsMediaGroup {
		t.Error("IsMediaGroup should be true")
	}

	if !orderInfo.HasImage {
		t.Error("HasImage should be true")
	}

	if orderInfo.MediaGroupID != "test_group_123" {
		t.Errorf("MediaGroupID = %v, want %v", orderInfo.MediaGroupID, "test_group_123")
	}

	expectedImageIDs := []string{"image1_large", "image2_large", "image3_large"}
	if len(orderInfo.ImageFileIDs) != len(expectedImageIDs) {
		t.Errorf("ImageFileIDs length = %v, want %v", len(orderInfo.ImageFileIDs), len(expectedImageIDs))
	}

	for i, expectedID := range expectedImageIDs {
		if orderInfo.ImageFileIDs[i] != expectedID {
			t.Errorf("ImageFileIDs[%d] = %v, want %v", i, orderInfo.ImageFileIDs[i], expectedID)
		}
	}

	// 检查兼容性字段
	if orderInfo.ImageFileID != "image1_large" {
		t.Errorf("ImageFileID = %v, want %v", orderInfo.ImageFileID, "image1_large")
	}
}

func TestMessageParser_IsSupplierReply(t *testing.T) {
	parser := NewMessageParser()

	tests := []struct {
		name     string
		message  *models.Message
		expected bool
	}{
		{
			name: "Valid supplier reply",
			message: &models.Message{
				Text: "success",
				ReplyToMessage: &models.Message{
					Text: "Submit Order ID: SUB123456",
				},
			},
			expected: true,
		},
		{
			name: "Message without reply",
			message: &models.Message{
				Text: "success",
			},
			expected: false,
		},
		{
			name: "Reply to non-order message",
			message: &models.Message{
				Text: "success",
				ReplyToMessage: &models.Message{
					Text: "Hello world",
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parser.IsSupplierReply(tt.message)
			if result != tt.expected {
				t.Errorf("IsSupplierReply() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMessageParser_ParseSupplierReply(t *testing.T) {
	parser := NewMessageParser()

	message := &models.Message{
		Text: "success",
		ReplyToMessage: &models.Message{
			Text: "Submit Order ID: SUB123456",
		},
	}

	reply := parser.ParseSupplierReply(message)

	if reply == nil {
		t.Fatal("ParseSupplierReply() returned nil")
	}

	if reply.SubmitOrderID != "SUB123456" {
		t.Errorf("SubmitOrderID = %v, want %v", reply.SubmitOrderID, "SUB123456")
	}

	if !reply.IsSuccess {
		t.Error("IsSuccess should be true")
	}

	if reply.Status != "success" {
		t.Errorf("Status = %v, want %v", reply.Status, "success")
	}
}

func TestGroupNameForwarding(t *testing.T) {
	// 这个测试用例验证转发到客服群时是否包含来源群组信息
	// 注意：这是一个概念性测试，实际测试需要mock相关服务

	// 测试消息格式（使用Telegram API获取真实群组名称）
	expectedFormats := []string{
		"📍 来源: 商户群: ABC支付商户群",  // 真实群组名称
		"🕐 时间: 2024-07-16 14:30:25",
		"📋 订单ID: ORDER123",
		"━━━━━━━━━━━━━━━━━━━━",
		"❌ 处理失败: Test failure reason",
	}

	for _, format := range expectedFormats {
		if format == "" {
			t.Error("Expected format should not be empty")
		}
	}

	// 测试Media Group格式
	mediaGroupFormats := []string{
		"📍 来源: 商户群: XYZ收款群",     // 真实群组名称
		"🕐 时间: 2024-07-16 14:30:25",
		"📋 订单ID: ORDER123",
		"📸 多图相册: test_group_123",
		"━━━━━━━━━━━━━━━━━━━━",
		"💡 提示: 这是多图相册中的一张图片",
	}

	for _, format := range mediaGroupFormats {
		if format == "" {
			t.Error("Media group format should not be empty")
		}
	}
}

func TestChatInfoParsing(t *testing.T) {
	// 测试ChatInfo结构体的解析
	testCases := []struct {
		name     string
		chatType string
		title    string
		expected string
	}{
		{
			name:     "Group chat with title",
			chatType: "group",
			title:    "ABC支付商户群",
			expected: "ABC支付商户群",
		},
		{
			name:     "Supergroup with title",
			chatType: "supergroup",
			title:    "XYZ供应商群",
			expected: "XYZ供应商群",
		},
		{
			name:     "Private chat",
			chatType: "private",
			title:    "",
			expected: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟ChatInfo结构（需要导入相关类型）
			// 这里只是概念性测试，实际需要proper import
			if tc.title != tc.expected {
				t.Errorf("Expected title %s, got %s", tc.expected, tc.title)
			}
		})
	}
}

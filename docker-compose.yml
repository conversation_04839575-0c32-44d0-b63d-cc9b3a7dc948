version: '3.8'

services:
  telegram-bot:
    build: .
    ports:
      - "8080:8080"
    environment:
      - PORT=${PORT:-8080}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - THIRD_PARTY_API_BASE_URL=${THIRD_PARTY_API_BASE_URL}
      - THIRD_PARTY_API_QUERY_PATH=${THIRD_PARTY_API_QUERY_PATH}
      - THIRD_API_KEY=${THIRD_API_KEY}
      - QRCODE_API_ACCOUNT_NO=${QRCODE_API_ACCOUNT_NO}
      - QRCODE_API_BASE_URL=${QRCODE_API_BASE_URL}
      - QRCODE_API_LICENSE_KEY=${QRCODE_API_LICENSE_KEY}
      - QRCODE_API_ACCESS_KEY=${QRCODE_API_ACCESS_KEY}
      - CUSTOMER_SERVICE_GROUP=${CUSTOMER_SERVICE_GROUP}
      - SUPPLIER_GROUP_TOPPAY=${SUPPLIER_GROUP_TOPPAY}
      - SUPPLIER_GROUP_VADERPAY=${SUPPLIER_GROUP_VADERPAY}
      - SUPPLIER_GROUP_THPAY=${SUPPLIER_GROUP_THPAY}
      - SUPPLIER_GROUP_EXKUB=${SUPPLIER_GROUP_EXKUB}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FILE_PATH=${LOG_FILE_PATH:-./logs/app.log}
      - LOG_MAX_SIZE=${LOG_MAX_SIZE:-100}
      - LOG_MAX_BACKUPS=${LOG_MAX_BACKUPS:-3}
      - LOG_MAX_AGE=${LOG_MAX_AGE:-28}
      - LOG_COMPRESS=${LOG_COMPRESS:-true}
      - LOG_CONSOLE=${LOG_CONSOLE:-true}
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

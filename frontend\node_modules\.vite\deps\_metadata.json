{"hash": "02006825", "configHash": "fd3d2227", "lockfileHash": "d27612ee", "browserHash": "2e265ee1", "optimized": {"@vueuse/core": {"src": "../../@vueuse/core/index.mjs", "file": "@vueuse_core.js", "fileHash": "bfeb435c", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "eb2ad748", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "1448e17a", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "84cf8606", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "034d6b4b", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "f3e24a72", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "7c992591", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "b8c34988", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "423ad31f", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-KE5LXLFG": {"file": "chunk-KE5LXLFG.js"}, "chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-FIAHBV72": {"file": "chunk-FIAHBV72.js"}, "chunk-PZ5AY32C": {"file": "chunk-PZ5AY32C.js"}}}
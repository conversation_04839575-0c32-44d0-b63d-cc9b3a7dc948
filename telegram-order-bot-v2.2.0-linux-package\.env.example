# 服务器配置
PORT=8080

# Telegram Bot配置
TELEGRAM_BOT_TOKEN=your_bot_token_here

# 第三方API配置
THIRD_PARTY_API_BASE_URL=https://aut.mb.rushingpay.com
THIRD_PARTY_API_QUERY_PATH=/ordercheck/query
THIRD_API_KEY=your_api_key_here

# 二维码API配置
QRCODE_API_ACCOUNT_NO=your_account_no_here
QRCODE_API_BASE_URL=https://api-fin.uexchange.io/v1/fin/bank/
QRCODE_API_LICENSE_KEY=your_license_key_here
QRCODE_API_ACCESS_KEY=your_access_key_here

# 群组配置
# 人工客服群ID
CUSTOMER_SERVICE_GROUP=-*************

# 供应商群组ID配置
# TAIP496-Cloud168-TOPPAY 对应 TopPayCloud168-Cloud168
SUPPLIER_GROUP_TOPPAY=-*************

# VaderPay-superkapoo888 对应 VDP CS - datodes / superkapoo888
SUPPLIER_GROUP_VADERPAY=-*************

# THPay-THPayCloudpay888 对应 M124-Cloudpay888-THPAY1 %
SUPPLIER_GROUP_THPAY=-*************

# ExkubScbRs-exkubscbIOne 对应 Cloudpay Order Checking
SUPPLIER_GROUP_EXKUB=-*************

# 商户群组ID（可选，如果需要限制特定商户群）
# MERCHANT_GROUP_1=-*************
# MERCHANT_GROUP_2=-*************

# 日志配置（可选）
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_COMPRESS=true
LOG_CONSOLE=true

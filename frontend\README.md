# 群组管理前端系统

基于 Vue 3 + TypeScript + Tailwind CSS + 自定义UI组件构建的群组管理前端界面。

## 功能特性

- ✅ 群组列表展示（支持分页）
- ✅ 群组搜索和筛选（按类型、支付机构、状态）
- ✅ 新增群组
- ✅ 编辑群组信息
- ✅ 删除群组（软删除）
- ✅ 响应式设计，支持移动端
- ✅ 现代化UI组件（基于shadcn-vue）

## 技术栈

- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **样式**: Tailwind CSS 3
- **UI组件**: 自定义组件（基于shadcn设计系统）
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **工具库**: @vueuse/core, lucide-vue-next

## 项目结构

```
frontend/
├── src/
│   ├── components/          # 组件目录
│   │   ├── ui/             # UI基础组件
│   │   ├── GroupFormDialog.vue    # 群组表单对话框
│   │   └── DeleteConfirmDialog.vue # 删除确认对话框
│   ├── views/              # 页面组件
│   │   └── GroupManagement.vue    # 群组管理主页面
│   ├── services/           # API服务层
│   │   ├── api.ts         # Axios配置
│   │   └── groupService.ts # 群组相关API
│   ├── types/              # TypeScript类型定义
│   │   └── group.ts       # 群组相关类型
│   ├── lib/                # 工具函数
│   │   └── utils.ts       # 通用工具函数
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── style.css          # 全局样式
├── package.json           # 项目依赖
├── vite.config.ts         # Vite配置
├── tailwind.config.js     # Tailwind配置
└── tsconfig.json          # TypeScript配置
```

## 安装和运行

### 1. 安装依赖

```bash
cd frontend
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

项目将在 `http://localhost:3000` 启动。

### 3. 构建生产版本

```bash
npm run build
```

### 4. 预览生产版本

```bash
npm run preview
```

## API配置

前端通过代理访问后端API，配置在 `vite.config.ts` 中：

```typescript
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
    },
  },
}
```

确保后端服务运行在 `http://localhost:8080`。

## 群组类型说明

- **商户群 (1)**: 处理订单消息的群组
- **供应商群 (2)**: 处理失败订单转发的群组，需要指定支付机构
- **客服群 (3)**: 人工客服处理的群组

## 界面预览

### 主界面功能
- 群组列表表格展示
- 实时搜索和筛选
- 分页导航
- 操作按钮（新增、编辑、删除）

### 表单功能
- 群组信息表单验证
- 动态字段要求（供应商群必填支付机构）
- 编辑模式下禁止修改Telegram ID

### 删除确认
- 详细的群组信息展示
- 安全的删除确认流程
- 软删除说明

## 开发说明

### 组件设计原则
- 使用 Composition API
- TypeScript 严格类型检查
- 响应式设计优先
- 组件复用和模块化

### 样式规范
- 使用 Tailwind CSS 工具类
- 遵循 shadcn-vue 设计系统
- 支持深色模式（预留）
- 移动端适配

### API集成
- 统一的错误处理
- 请求/响应拦截器
- TypeScript 类型安全
- 防抖搜索优化

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS telegram_order_bot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE telegram_order_bot;

-- 创建群聊表
CREATE TABLE IF NOT EXISTS `groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL COMMENT '群聊名称',
  `telegram_group_id` varchar(50) NOT NULL COMMENT 'Telegram群组ID',
  `payment_institution` varchar(255) DEFAULT NULL COMMENT '支付机构',
  `group_type` tinyint NOT NULL COMMENT '群聊类型(1:商户群,2:供应商群,3:客服群)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_telegram_group_id` (`telegram_group_id`),
  KEY `idx_group_type` (`group_type`),
  KEY `idx_payment_institution` (`payment_institution`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据
INSERT INTO `groups` (`group_name`, `telegram_group_id`, `payment_institution`, `group_type`, `is_active`) VALUES
('测试商户群1', '-1001234567890', NULL, 1, 1),
('测试商户群2', '-1001234567891', NULL, 1, 1),
('支付宝供应商群', '-1001234567892', '支付宝', 2, 1),
('微信供应商群', '-1001234567893', '微信支付', 2, 1),
('银行卡供应商群', '-1001234567894', '银行卡', 2, 1),
('客服群', '-1001234567895', NULL, 3, 1);

-- 查看插入的数据
SELECT 
    id,
    group_name,
    telegram_group_id,
    payment_institution,
    CASE group_type 
        WHEN 1 THEN '商户群'
        WHEN 2 THEN '供应商群'
        WHEN 3 THEN '客服群'
        ELSE '未知'
    END as group_type_name,
    is_active,
    created_at,
    updated_at
FROM `groups`
ORDER BY group_type, id;

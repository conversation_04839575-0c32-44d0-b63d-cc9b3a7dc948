package services

import (
	"net/http"
	"net/http/httptest"
	"telegram-order-bot/internal/config"
	"testing"
	"time"
)

// TestOrderQueryTimeout 测试订单查询超时功能
func TestOrderQueryTimeout(t *testing.T) {
	// 创建一个会延迟响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 延迟35秒，超过30秒的超时设置
		time.Sleep(35 * time.Second)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"success": true}`))
	}))
	defer server.Close()

	// 创建订单服务配置
	apiCfg := config.ThirdPartyAPIConfig{
		BaseURL:   server.URL,
		QueryPath: "/query",
		Key:       "test_key",
	}

	qrCfg := config.QRCodeAPIConfig{
		BaseURL:    "https://qr.example.com/",
		AccountNo:  "test_account",
		LicenseKey: "test_license",
		AccessKey:  "test_access",
	}

	// 创建订单服务
	orderService := NewOrderService(apiCfg, qrCfg)

	// 测试订单号
	testOrderID := "TIMEOUT_TEST_123"

	// 记录开始时间
	startTime := time.Now()

	// 执行查询（应该在30秒后超时）
	_, err := orderService.QueryOrder(testOrderID)

	// 记录结束时间
	elapsed := time.Since(startTime)

	// 验证是否在合理时间内超时（应该在30-32秒之间）
	if elapsed < 29*time.Second || elapsed > 35*time.Second {
		t.Errorf("Expected timeout around 30 seconds, but took %v", elapsed)
	}

	// 验证返回了超时错误
	if err == nil {
		t.Error("Expected timeout error, but got nil")
	}

	t.Logf("Query timed out after %v with error: %v", elapsed, err)
}

// TestQRCodeVerificationTimeout 测试二维码验证超时功能
func TestQRCodeVerificationTimeout(t *testing.T) {
	// 创建一个会延迟响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 延迟35秒，超过30秒的超时设置
		time.Sleep(35 * time.Second)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": true}`))
	}))
	defer server.Close()

	// 创建订单服务配置
	apiCfg := config.ThirdPartyAPIConfig{
		BaseURL:   "https://api.example.com",
		QueryPath: "/query",
		Key:       "test_key",
	}

	qrCfg := config.QRCodeAPIConfig{
		BaseURL:    server.URL + "/",
		AccountNo:  "test_account",
		LicenseKey: "test_license",
		AccessKey:  "test_access",
	}

	// 创建订单服务
	orderService := NewOrderService(apiCfg, qrCfg)

	// 测试二维码内容
	testQRContent := "test_qr_content_123"

	// 记录开始时间
	startTime := time.Now()

	// 执行验证（应该在30秒后超时）
	_, err := orderService.VerifyVoucher(testQRContent)

	// 记录结束时间
	elapsed := time.Since(startTime)

	// 验证是否在合理时间内超时（应该在30-32秒之间）
	if elapsed < 29*time.Second || elapsed > 35*time.Second {
		t.Errorf("Expected timeout around 30 seconds, but took %v", elapsed)
	}

	// 验证返回了超时错误
	if err == nil {
		t.Error("Expected timeout error, but got nil")
	}

	t.Logf("QR verification timed out after %v with error: %v", elapsed, err)
}

// TestContextCancellation 测试上下文取消功能
func TestContextCancellation(t *testing.T) {
	// 创建一个永远不会响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查请求是否被取消
		select {
		case <-r.Context().Done():
			t.Log("Request was properly cancelled")
			return
		case <-time.After(40 * time.Second):
			// 如果40秒后还没被取消，说明有问题
			t.Error("Request should have been cancelled")
		}
	}))
	defer server.Close()

	// 创建订单服务配置
	apiCfg := config.ThirdPartyAPIConfig{
		BaseURL:   server.URL,
		QueryPath: "/query",
		Key:       "test_key",
	}

	qrCfg := config.QRCodeAPIConfig{
		BaseURL:    "https://qr.example.com/",
		AccountNo:  "test_account",
		LicenseKey: "test_license",
		AccessKey:  "test_access",
	}

	// 创建订单服务
	orderService := NewOrderService(apiCfg, qrCfg)

	// 测试订单号
	testOrderID := "CANCEL_TEST_123"

	// 执行查询（应该在30秒后被取消）
	_, err := orderService.QueryOrder(testOrderID)

	// 验证返回了错误
	if err == nil {
		t.Error("Expected cancellation error, but got nil")
	}

	t.Logf("Request was cancelled with error: %v", err)
}

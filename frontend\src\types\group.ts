// 群组类型枚举
export enum GroupType {
  MERCHANT = 1,    // 商户群
  SUPPLIER = 2,    // 供应商群
  CUSTOMER_SERVICE = 3  // 客服群
}

// 群组类型标签映射
export const GroupTypeLabels: Record<GroupType, string> = {
  [GroupType.MERCHANT]: '商户群',
  [GroupType.SUPPLIER]: '供应商群',
  [GroupType.CUSTOMER_SERVICE]: '客服群'
}

// 群组数据模型
export interface Group {
  id: number
  group_name: string
  telegram_group_id: string
  payment_institution: string
  group_type: GroupType
  is_active: boolean
  created_at: string
  updated_at: string
}

// 创建群组请求
export interface CreateGroupRequest {
  group_name: string
  telegram_group_id: string
  payment_institution?: string
  group_type: GroupType
}

// 更新群组请求
export interface UpdateGroupRequest {
  group_name?: string
  payment_institution?: string
  group_type?: GroupType
  is_active?: boolean
}

// 分页请求参数
export interface PaginationRequest {
  page?: number
  page_size?: number
  group_type?: GroupType
  payment_institution?: string
  is_active?: boolean
  search?: string
}

// 分页响应数据
export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// API响应基础结构
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  pagination?: {
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

// 群组列表响应
export type GroupListResponse = ApiResponse<Group[]>

// 群组详情响应
export type GroupDetailResponse = ApiResponse<Group>

// 群组分页响应
export type GroupPaginationResponse = ApiResponse<Group[]> & {
  pagination: {
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

# 手动部署指南

## 部署环境要求

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+/CentOS 7+) 或 Windows Server
- **Go版本**: Go 1.21 或更高版本
- **内存**: 最少 512MB，推荐 1GB+
- **磁盘空间**: 最少 1GB 可用空间
- **网络**: 能够访问 Telegram API 和第三方 API

### 网络要求
- **出站端口**: 443 (HTTPS)
- **入站端口**: 8080 (可配置)
- **域名**: 需要有效的域名和SSL证书（用于Webhook）

## 部署步骤

### 第1步: 环境准备

#### 1.1 安装Go语言环境

**Linux (Ubuntu/Debian):**
```bash
# 下载Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz

# 解压到/usr/local
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

**Windows:**
1. 下载Go安装包: https://go.dev/dl/go1.21.0.windows-amd64.msi
2. 运行安装程序
3. 验证安装: `go version`

#### 1.2 创建部署目录
```bash
# 创建应用目录
sudo mkdir -p /opt/telegram-bot
cd /opt/telegram-bot

# 创建必要的子目录
mkdir -p logs
mkdir -p bin
mkdir -p config
```

### 第2步: 获取源代码

```bash
# 方式1: 使用git克隆
git clone <your-repository-url> /opt/telegram-bot
cd /opt/telegram-bot

# 方式2: 手动上传文件（如果没有git）
# 将项目文件上传到 /opt/telegram-bot 目录
```

### 第3步: 配置文件准备

#### 3.1 必需的配置文件清单

**核心配置文件:**
- `.env` - 环境变量配置（必需）
- `main.go` - 主程序文件
- `go.mod` - Go模块文件
- `go.sum` - 依赖校验文件

**目录结构:**
```
/opt/telegram-bot/
├── .env                    # 环境变量配置
├── main.go                 # 主程序
├── go.mod                  # Go模块文件
├── go.sum                  # 依赖校验文件
├── internal/               # 内部包目录
│   ├── config/
│   ├── handlers/
│   ├── logger/
│   ├── models/
│   └── services/
├── logs/                   # 日志目录
└── bin/                    # 编译后的二进制文件
```

#### 3.2 创建.env配置文件

```bash
# 复制模板文件
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

**完整的.env配置内容:**
```bash
# 服务器配置
PORT=8080

# Telegram Bot配置
TELEGRAM_BOT_TOKEN=你的_bot_token_这里

# 第三方API配置
THIRD_PARTY_API_BASE_URL=https://aut.mb.rushingpay.com
THIRD_PARTY_API_QUERY_PATH=/ordercheck/query
THIRD_API_KEY=你的_api_密钥_这里

# 二维码API配置
QRCODE_API_ACCOUNT_NO=你的_账户号_这里
QRCODE_API_BASE_URL=https://api-fin.uexchange.io/v1/fin/bank/
QRCODE_API_LICENSE_KEY=你的_许可证密钥_这里
QRCODE_API_ACCESS_KEY=你的_访问密钥_这里

# 群组配置
CUSTOMER_SERVICE_GROUP=-*************
SUPPLIER_GROUP_TOPPAY=-*************
SUPPLIER_GROUP_VADERPAY=-*************
SUPPLIER_GROUP_THPAY=-*************
SUPPLIER_GROUP_EXKUB=-*************

# 日志配置（可选）
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_COMPRESS=true
LOG_CONSOLE=true
```

### 第4步: 编译应用程序

```bash
# 进入项目目录
cd /opt/telegram-bot

# 下载依赖
go mod tidy
go mod download

# 编译应用程序
go build -o bin/telegram-order-bot .

# 验证编译结果
ls -la bin/
./bin/telegram-order-bot --help  # 测试是否能正常运行
```

### 第5步: 配置验证

```bash
# 使用配置检查工具
chmod +x scripts/check-config.sh
./scripts/check-config.sh

# 或手动检查关键配置
echo "检查环境变量..."
source .env
echo "Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
echo "API Base URL: $THIRD_PARTY_API_BASE_URL"
echo "Port: $PORT"
```

### 第6步: 创建系统服务（推荐）

#### 6.1 创建systemd服务文件

```bash
sudo nano /etc/systemd/system/telegram-bot.service
```

**服务文件内容:**
```ini
[Unit]
Description=Telegram Order Bot
After=network.target

[Service]
Type=simple
User=telegram-bot
Group=telegram-bot
WorkingDirectory=/opt/telegram-bot
ExecStart=/opt/telegram-bot/bin/telegram-order-bot
Restart=always
RestartSec=5
Environment=PATH=/usr/local/go/bin:/usr/bin:/bin
EnvironmentFile=/opt/telegram-bot/.env

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-bot

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/telegram-bot/logs

[Install]
WantedBy=multi-user.target
```

#### 6.2 创建专用用户

```bash
# 创建系统用户
sudo useradd -r -s /bin/false telegram-bot

# 设置目录权限
sudo chown -R telegram-bot:telegram-bot /opt/telegram-bot
sudo chmod +x /opt/telegram-bot/bin/telegram-order-bot
```

#### 6.3 启动服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable telegram-bot

# 启动服务
sudo systemctl start telegram-bot

# 检查服务状态
sudo systemctl status telegram-bot
```

### 第7步: 设置Webhook

```bash
# 设置环境变量
export BOT_TOKEN="你的_bot_token"
export WEBHOOK_URL="https://你的域名.com"

# 设置Webhook
curl -X POST "https://api.telegram.org/bot$BOT_TOKEN/setWebhook" \
     -H "Content-Type: application/json" \
     -d "{\"url\": \"$WEBHOOK_URL/webhook/$BOT_TOKEN\"}"

# 验证Webhook设置
curl "https://api.telegram.org/bot$BOT_TOKEN/getWebhookInfo"
```

### 第8步: 配置反向代理（Nginx）

#### 8.1 安装Nginx

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 8.2 配置Nginx

```bash
sudo nano /etc/nginx/sites-available/telegram-bot
```

**Nginx配置内容:**
```nginx
server {
    listen 80;
    server_name 你的域名.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name 你的域名.com;

    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 代理到应用程序
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        access_log off;
    }

    # 日志配置
    access_log /var/log/nginx/telegram-bot.access.log;
    error_log /var/log/nginx/telegram-bot.error.log;
}
```

#### 8.3 启用配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/telegram-bot /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 部署验证

### 1. 服务状态检查

```bash
# 检查应用程序状态
sudo systemctl status telegram-bot

# 检查端口监听
sudo netstat -tlnp | grep :8080

# 检查进程
ps aux | grep telegram-order-bot
```

### 2. 功能测试

```bash
# 健康检查
curl http://localhost:8080/health

# 通过域名访问
curl https://你的域名.com/health

# 检查Webhook状态
curl "https://api.telegram.org/bot你的_bot_token/getWebhookInfo"
```

### 3. 日志检查

```bash
# 查看应用日志
tail -f /opt/telegram-bot/logs/app.log

# 查看系统日志
sudo journalctl -u telegram-bot -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/telegram-bot.access.log
sudo tail -f /var/log/nginx/telegram-bot.error.log
```

## 维护和监控

### 日常维护命令

```bash
# 重启服务
sudo systemctl restart telegram-bot

# 查看服务日志
sudo journalctl -u telegram-bot --since "1 hour ago"

# 更新应用程序
cd /opt/telegram-bot
git pull
go build -o bin/telegram-order-bot .
sudo systemctl restart telegram-bot

# 清理日志
find /opt/telegram-bot/logs -name "*.log.*" -mtime +30 -delete
```

### 监控脚本

创建监控脚本 `/opt/telegram-bot/scripts/monitor.sh`:

```bash
#!/bin/bash
# 简单的监控脚本

# 检查服务状态
if ! systemctl is-active --quiet telegram-bot; then
    echo "$(date): Service is down, restarting..." >> /var/log/telegram-bot-monitor.log
    sudo systemctl restart telegram-bot
fi

# 检查健康端点
if ! curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "$(date): Health check failed" >> /var/log/telegram-bot-monitor.log
fi
```

添加到crontab:
```bash
# 每5分钟检查一次
*/5 * * * * /opt/telegram-bot/scripts/monitor.sh
```

## 故障排查

### 常见问题

1. **服务无法启动**
   - 检查配置文件语法
   - 验证环境变量
   - 查看系统日志

2. **Webhook设置失败**
   - 确认域名可访问
   - 检查SSL证书
   - 验证Bot Token

3. **API调用失败**
   - 检查网络连接
   - 验证API配置
   - 查看应用日志

通过以上步骤，您就可以完成Telegram订单处理机器人的手动部署。

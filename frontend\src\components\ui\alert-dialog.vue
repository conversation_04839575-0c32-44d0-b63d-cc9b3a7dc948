<template>
  <div v-if="open" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- Overlay -->
    <div 
      class="fixed inset-0 bg-black/80" 
      @click="$emit('update:open', false)"
    ></div>
    
    <!-- <PERSON><PERSON> Dialog Content -->
    <div class="relative z-50 grid w-full max-w-md gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg">
      <div class="flex flex-col space-y-2 text-center sm:text-left">
        <h2 class="text-lg font-semibold">
          <slot name="title" />
        </h2>
        <p class="text-sm text-muted-foreground">
          <slot name="description" />
        </p>
      </div>
      <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
        <slot name="actions" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  open: boolean
}

defineProps<Props>()
defineEmits<{
  'update:open': [value: boolean]
}>()
</script>

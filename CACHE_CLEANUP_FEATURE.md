# 群组缓存清除功能

## 📋 功能概述

当群组表进行删除或更新操作时，系统会自动清除机器人对应的缓存，确保缓存数据与数据库保持一致。

## 🔧 技术实现

### 缓存类型

机器人服务中维护了两种群组相关的缓存：

1. **群组类型缓存** (`groupTypes sync.Map`)
   - 键：`int64` (Telegram Chat ID)
   - 值：`models.GroupType` (群组类型)

2. **群组名称缓存** (`groupNames sync.Map`)
   - 键：`int64` (Telegram Chat ID)  
   - 值：`string` (群组名称)

### 缓存清除机制

#### 1. 回调函数设置
```go
// 在BotService初始化时设置缓存清除回调
if groupService != nil {
    groupService.SetCacheCleanupCallback(service.clearGroupCache)
}
```

#### 2. 缓存清除方法
```go
// clearGroupCache 清除指定群组的缓存
func (b *BotService) clearGroupCache(telegramGroupID string) {
    // 将字符串ID转换为int64
    chatID, err := strconv.ParseInt(telegramGroupID, 10, 64)
    if err != nil {
        // 记录错误日志
        return
    }

    // 清除群组类型缓存
    b.groupTypes.LoadAndDelete(chatID)
    
    // 清除群组名称缓存
    b.groupNames.LoadAndDelete(chatID)
}
```

#### 3. 触发时机

缓存清除会在以下操作时自动触发：

- **软删除群组** (`DeleteGroup`)
- **硬删除群组** (`HardDeleteGroup`)
- **更新群组信息** (`UpdateGroup`)

## 🚀 使用示例

### 通过API删除群组

```bash
# 删除群组（软删除）
curl -X DELETE http://localhost:8080/api/v1/groups/123

# 响应
{
  "success": true,
  "message": "Group deleted successfully"
}
```

### 通过API更新群组

```bash
# 更新群组信息
curl -X PUT http://localhost:8080/api/v1/groups/123 \
  -H "Content-Type: application/json" \
  -d '{
    "group_name": "新的群组名称",
    "group_type": 1
  }'

# 响应
{
  "success": true,
  "data": {...},
  "message": "Group updated successfully"
}
```

## 📊 日志记录

系统会记录详细的缓存清除日志：

```json
{
  "chat_id": -1001234567890,
  "level": "info",
  "msg": "Cleared group type cache",
  "telegram_group_id": "-1001234567890",
  "time": "2025-07-25 23:19:49"
}

{
  "chat_id": -1001234567890,
  "level": "info", 
  "msg": "Cleared group name cache",
  "telegram_group_id": "-1001234567890",
  "time": "2025-07-25 23:19:49"
}

{
  "chat_id": -1001234567890,
  "level": "info",
  "msg": "Group cache cleanup completed",
  "telegram_group_id": "-1001234567890", 
  "time": "2025-07-25 23:19:49"
}
```

## 🧪 测试验证

### 单元测试

项目包含完整的单元测试来验证缓存清除功能：

```bash
# 运行缓存清除相关测试
go test -v ./internal/services -run TestGroupCache

# 运行所有群组服务测试
go test -v ./internal/services -run TestGroupService
```

### 测试覆盖

- ✅ 缓存清除基本功能
- ✅ 无效ID处理
- ✅ 回调函数设置
- ✅ BotService与GroupService集成

## 🔄 工作流程

1. **用户操作** - 通过API删除/更新群组
2. **数据库操作** - GroupService执行数据库操作
3. **回调触发** - 操作成功后触发缓存清除回调
4. **缓存清除** - BotService清除对应的缓存数据
5. **日志记录** - 记录缓存清除操作日志

## ⚠️ 注意事项

1. **线程安全** - 使用`sync.Map`确保并发安全
2. **错误处理** - 无效ID不会导致程序崩溃，只记录错误日志
3. **性能影响** - 缓存清除操作非常轻量，不会影响系统性能
4. **数据一致性** - 确保缓存与数据库数据保持一致

## 🔧 配置要求

无需额外配置，功能在系统启动时自动初始化。

## 📈 性能优化

- 使用`LoadAndDelete`原子操作
- 异步日志记录
- 最小化锁竞争

---

**版本**: v2.2.0+  
**更新时间**: 2025-07-25

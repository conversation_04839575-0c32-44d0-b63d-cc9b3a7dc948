#!/bin/bash

# Script to get Telegram group ID

BOT_TOKEN="**********************************************"

echo "🔍 Telegram Group ID Retrieval Tool"
echo "=========================="
echo ""
echo "📋 Usage:"
echo "1. Add bot @AutoCheckingTestBot to your group"
echo "2. Send any message in the group (like /start or hello)"
echo "3. Run this script to view recent updates"
echo ""

echo "🔄 Getting recent updates..."
response=$(curl -s "https://api.telegram.org/bot${BOT_TOKEN}/getUpdates?limit=10&offset=-10")

echo "📊 Recent updates:"
echo "$response" | python3 -c "
import json
import sys

try:
    data = json.load(sys.stdin)
    if data['ok'] and data['result']:
        print('Found the following groups/chats:')
        print('=' * 50)
        
        seen_chats = set()
        for update in data['result']:
            if 'message' in update:
                chat = update['message']['chat']
                chat_id = chat['id']
                
                if chat_id not in seen_chats:
                    seen_chats.add(chat_id)
                    
                    chat_type = chat['type']
                    title = chat.get('title', chat.get('first_name', 'Unknown'))
                    
                    print(f'类型: {chat_type}')
                    print(f'名称: {title}')
                    print(f'ID: {chat_id}')
                    
                    if chat_type in ['group', 'supergroup']:
                        print(f'✅ 群组ID (用于配置): {chat_id}')
                    elif chat_type == 'private':
                        print(f'💬 私聊ID: {chat_id}')
                    
                    print('-' * 30)
        
        if not seen_chats:
            print('❌ 没有找到任何聊天记录')
            print('请确保:')
            print('1. 机器人已添加到群组')
            print('2. 在群组中发送了消息')
            print('3. 机器人有接收消息的权限')
    else:
        print('❌ 获取更新失败')
        print('响应:', data)
        
except Exception as e:
    print(f'❌ 解析响应失败: {e}')
    print('原始响应:')
    print(sys.stdin.read())
"

echo ""
echo "💡 提示："
echo "- 群组ID通常是负数（如 -1002898629289）"
echo "- 私聊ID通常是正数"
echo "- 将群组ID复制到 .env 文件的 CUSTOMER_SERVICE_GROUP 配置中"
echo ""
echo "📝 配置示例："
echo "CUSTOMER_SERVICE_GROUP=-1002898629289"
